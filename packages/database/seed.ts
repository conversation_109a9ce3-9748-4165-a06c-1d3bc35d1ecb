import { PrismaClient } from '@prisma/client';
import { PasswordHelper } from '@crm/shared';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create test organization
  const organization = await prisma.organization.create({
    data: {
      name: 'Acme Real Estate',
      slug: 'acme-real-estate',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Main St, Anytown, USA',
      website: 'https://acme-realestate.com',
      timezone: 'America/New_York',
      currency: 'USD',
    },
  });

  console.log('✅ Created organization:', organization.name);

  // Create super admin user
  const hashedPassword = await PasswordHelper.hash('admin123');
  const superAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      organizationId: organization.id,
      role: 'SUPER_ADMIN',
      isVerified: true,
    },
  });

  console.log('✅ Created super admin user:', superAdmin.email);

  // Create regular admin user
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'John',
      lastName: 'Manager',
      organizationId: organization.id,
      role: 'ADMIN',
      isVerified: true,
    },
  });

  console.log('✅ Created admin user:', admin.email);

  // Create agent user
  const agent = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Jane',
      lastName: 'Agent',
      organizationId: organization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  console.log('✅ Created agent user:', agent.email);

  console.log('\n🎉 Database seeded successfully!');
  console.log('\n📋 Test Credentials:');
  console.log('   Super Admin: <EMAIL> / admin123');
  console.log('   Admin: <EMAIL> / admin123');
  console.log('   Agent: <EMAIL> / admin123');
  console.log(`   Organization ID: ${organization.id}`);
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
