import Joi from 'joi';

// Common schemas
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
});

export const idSchema = Joi.object({
  id: Joi.string().required(),
});

// Auth schemas
export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  organizationId: Joi.string().required(),
  role: Joi.string().valid('ADMIN', 'MANAGER', 'AGENT', 'VIEWER').default('AGENT'),
});

export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(8).required(),
});

export const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required(),
});

// Organization schemas
export const createOrganizationSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  slug: Joi.string().min(2).max(50).pattern(/^[a-z0-9-]+$/).required(),
  domain: Joi.string().domain().optional(),
  logo: Joi.string().uri().optional(),
  address: Joi.string().max(255).optional(),
  phone: Joi.string().max(20).optional(),
  email: Joi.string().email().optional(),
  website: Joi.string().uri().optional(),
  timezone: Joi.string().default('UTC'),
  currency: Joi.string().length(3).default('USD'),
  settings: Joi.object().optional(),
});

export const updateOrganizationSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  slug: Joi.string().min(2).max(50).pattern(/^[a-z0-9-]+$/).optional(),
  domain: Joi.string().domain().allow(null).optional(),
  logo: Joi.string().uri().allow(null).optional(),
  address: Joi.string().max(255).allow(null).optional(),
  phone: Joi.string().max(20).allow(null).optional(),
  email: Joi.string().email().allow(null).optional(),
  website: Joi.string().uri().allow(null).optional(),
  timezone: Joi.string().optional(),
  currency: Joi.string().length(3).optional(),
  settings: Joi.object().allow(null).optional(),
});

// Additional schemas can be added here as needed for future features
