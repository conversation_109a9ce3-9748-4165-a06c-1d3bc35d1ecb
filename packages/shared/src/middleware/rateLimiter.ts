import rateLimit from 'express-rate-limit';
import { ResponseHelper } from '../utils/response';
import { Request, Response, NextFunction } from 'express';

export const createRateLimiter = (options: {
  windowMs?: number;
  max?: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes
    max: options.max || 100, // limit each IP to 100 requests per windowMs
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    handler: (req, res) => {
      ResponseHelper.error(
        res,
        'RATE_LIMIT_EXCEEDED',
        429,
        options.message || 'Too many requests, please try again later'
      );
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  });
};

// Pre-configured rate limiters
export const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 300, // 100 requests per 15 minutes
});

export const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 login attempts per 15 minutes
  message: 'Too many authentication attempts, please try again later',
  skipSuccessfulRequests: true,
});

export const apiLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute
});

const superAdminLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 10000,
  message: 'Too many requests for super admin, please try again later',
});
const adminLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 10000,
  message: 'Too many requests for admin, please try again later',
});
const managerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 5000,
  message: 'Too many requests for manager, please try again later',
});
const agentLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 5000,
  message: 'Too many requests for agent, please try again later',
});
const viewerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000,
  max: 2000,
  message: 'Too many requests for viewer, please try again later',
});

interface User {
  role?: 'SUPER_ADMIN' | 'ADMIN' | 'MANAGER' | 'AGENT' | 'VIEWER' | string;
}

interface RoleRequest extends Request {
  user?: User;
}

type RateLimiterMiddleware = (req: Request, res: Response, next: NextFunction) => void;

export const clientsLimiter: RateLimiterMiddleware = (req: RoleRequest, res: Response, next: NextFunction) => {
  const role = req.user?.role;
  switch (role) {
    case 'SUPER_ADMIN':
      return superAdminLimiter(req, res, next);
    case 'ADMIN':
      return adminLimiter(req, res, next);
    case 'MANAGER':
      return managerLimiter(req, res, next);
    case 'AGENT':
      return agentLimiter(req, res, next);
    case 'VIEWER':
      return viewerLimiter(req, res, next);
    default:
      return viewerLimiter(req, res, next);
  }
};