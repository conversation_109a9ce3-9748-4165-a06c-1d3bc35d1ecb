import request from 'supertest';
import { PrismaClient } from '@crm/database';
import { PasswordHelper } from '@crm/shared';

const API_BASE = 'http://localhost:3000/api';
const prisma = new PrismaClient();

describe('API Gateway Integration Tests', () => {
  let testData: any;

  beforeAll(async () => {
    // Setup test data
    testData = await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await prisma.$disconnect();
  });

  describe('Complete Authentication & Organizations Flow', () => {
    let superAdminTokens: any;
    let adminTokens: any;
    let agentTokens: any;
    let newOrganizationId: string;

    it('should complete full authentication flow', async () => {
      // 1. Login as Super Admin
      const superAdminLogin = await request(API_BASE)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(200);

      expect(superAdminLogin.body.success).toBe(true);
      expect(superAdminLogin.body.data.user.role).toBe('SUPER_ADMIN');
      expect(superAdminLogin.body.data.user.organization).toBeDefined();
      superAdminTokens = superAdminLogin.body.data;

      // 2. Login as Admin
      const adminLogin = await request(API_BASE)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(200);

      expect(adminLogin.body.success).toBe(true);
      expect(adminLogin.body.data.user.role).toBe('ADMIN');
      adminTokens = adminLogin.body.data;

      // 3. Login as Agent
      const agentLogin = await request(API_BASE)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(200);

      expect(agentLogin.body.success).toBe(true);
      expect(agentLogin.body.data.user.role).toBe('AGENT');
      agentTokens = agentLogin.body.data;

      // 4. Verify all users belong to the same organization
      expect(superAdminTokens.user.organizationId).toBe(adminTokens.user.organizationId);
      expect(adminTokens.user.organizationId).toBe(agentTokens.user.organizationId);
    });

    it('should test organization management flow', async () => {
      // 1. Super Admin creates new organization
      const createOrgResponse = await request(API_BASE)
        .post('/organizations')
        .set('Authorization', `Bearer ${superAdminTokens.accessToken}`)
        .send({
          name: 'API Test New Organization',
          slug: 'api-test-new-org',
          email: '<EMAIL>',
          phone: '******-1111',
          timezone: 'UTC',
          currency: 'USD',
        })
        .expect(201);

      expect(createOrgResponse.body.success).toBe(true);
      expect(createOrgResponse.body.data.name).toBe('API Test New Organization');
      newOrganizationId = createOrgResponse.body.data.id;

      // 2. Admin tries to create organization (should fail)
      await request(API_BASE)
        .post('/organizations')
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .send({
          name: 'Admin Created Org',
          slug: 'admin-created-org',
          timezone: 'UTC',
          currency: 'USD',
        })
        .expect(403);

      // 3. Admin gets their organization
      const getOrgResponse = await request(API_BASE)
        .get('/organizations')
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(200);

      expect(getOrgResponse.body.success).toBe(true);
      expect(getOrgResponse.body.data).toHaveLength(1);
      expect(getOrgResponse.body.data[0].id).toBe(testData.testOrganization.id);

      // 4. Admin gets specific organization
      const getSpecificOrgResponse = await request(API_BASE)
        .get(`/organizations/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(200);

      expect(getSpecificOrgResponse.body.success).toBe(true);
      expect(getSpecificOrgResponse.body.data.id).toBe(testData.testOrganization.id);

      // 5. Admin tries to access different organization (should fail)
      await request(API_BASE)
        .get(`/organizations/${newOrganizationId}`)
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(403);

      // 6. Admin updates their organization
      const updateOrgResponse = await request(API_BASE)
        .put(`/organizations/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .send({
          name: 'Updated API Test Organization',
          phone: '******-9999',
        })
        .expect(200);

      expect(updateOrgResponse.body.success).toBe(true);
      expect(updateOrgResponse.body.data.name).toBe('Updated API Test Organization');

      // 7. Agent tries to update organization (should fail)
      await request(API_BASE)
        .put(`/organizations/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${agentTokens.accessToken}`)
        .send({
          name: 'Agent Hacked Name',
        })
        .expect(403);
    });

    it('should test cross-service authentication validation', async () => {
      // 1. Get current user info (auth service)
      const getCurrentUserResponse = await request(API_BASE)
        .get('/auth/me')
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(200);

      expect(getCurrentUserResponse.body.success).toBe(true);
      expect(getCurrentUserResponse.body.data.organizationId).toBe(testData.testOrganization.id);

      // 2. Use same token to access organizations (organizations service)
      const getOrganizationsResponse = await request(API_BASE)
        .get('/organizations')
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(200);

      expect(getOrganizationsResponse.body.success).toBe(true);
      expect(getOrganizationsResponse.body.data[0].id).toBe(testData.testOrganization.id);

      // 3. Verify organization isolation
      const orgFromAuth = getCurrentUserResponse.body.data.organizationId;
      const orgFromOrganizations = getOrganizationsResponse.body.data[0].id;
      expect(orgFromAuth).toBe(orgFromOrganizations);
    });

    it('should test token refresh flow', async () => {
      // 1. Refresh admin token
      const refreshResponse = await request(API_BASE)
        .post('/auth/refresh')
        .send({
          refreshToken: adminTokens.refreshToken,
        })
        .expect(200);

      expect(refreshResponse.body.success).toBe(true);
      expect(refreshResponse.body.data.accessToken).toBeDefined();
      expect(refreshResponse.body.data.refreshToken).toBeDefined();

      // 2. Use new token to access protected resource
      const newAccessToken = refreshResponse.body.data.accessToken;
      const protectedResponse = await request(API_BASE)
        .get('/organizations')
        .set('Authorization', `Bearer ${newAccessToken}`)
        .expect(200);

      expect(protectedResponse.body.success).toBe(true);
    });

    it('should test user registration with organization validation', async () => {
      // 1. Register new user in existing organization
      const registerResponse = await request(API_BASE)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'newpassword123',
          firstName: 'New',
          lastName: 'User',
          organizationId: testData.testOrganization.id,
          role: 'AGENT',
        })
        .expect(201);

      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.organizationId).toBe(testData.testOrganization.id);

      // 2. Try to register user in non-existent organization
      await request(API_BASE)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Invalid',
          lastName: 'User',
          organizationId: 'invalid-org-id',
          role: 'AGENT',
        })
        .expect(400);

      // 3. Login with new user and verify organization access
      const newUserLogin = await request(API_BASE)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'newpassword123',
        })
        .expect(200);

      expect(newUserLogin.body.data.user.organizationId).toBe(testData.testOrganization.id);

      // 4. New user can access their organization
      await request(API_BASE)
        .get('/organizations')
        .set('Authorization', `Bearer ${newUserLogin.body.data.accessToken}`)
        .expect(200);
    });

    it('should test error handling and validation', async () => {
      // 1. Test invalid authentication
      await request(API_BASE)
        .get('/organizations')
        .set('Authorization', 'Bearer invalid.token.here')
        .expect(401);

      // 2. Test missing authentication
      await request(API_BASE)
        .get('/organizations')
        .expect(401);

      // 3. Test invalid organization data
      await request(API_BASE)
        .post('/organizations')
        .set('Authorization', `Bearer ${superAdminTokens.accessToken}`)
        .send({
          name: 'A', // Too short
          slug: 'invalid slug with spaces',
        })
        .expect(400);

      // 4. Test invalid login credentials
      await request(API_BASE)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
        .expect(401);
    });

    it('should test logout flow', async () => {
      // 1. Logout user
      const logoutResponse = await request(API_BASE)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${agentTokens.accessToken}`)
        .expect(200);

      expect(logoutResponse.body.success).toBe(true);
      expect(logoutResponse.body.message).toBe('Logout successful');
    });
  });
});

// Helper functions
async function setupTestData() {
  // Clean up existing test data
  await cleanupTestData();

  // Create test organization
  const testOrganization = await prisma.organization.create({
    data: {
      name: 'API Test Organization',
      slug: 'api-test-org',
      email: '<EMAIL>',
      phone: '******-0123',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  // Create test users
  const hashedPassword = await PasswordHelper.hash('password123');

  const superAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      organizationId: testOrganization.id,
      role: 'SUPER_ADMIN',
      isVerified: true,
    },
  });

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'ADMIN',
      isVerified: true,
    },
  });

  const agent = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Agent',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  return {
    testOrganization,
    superAdmin,
    admin,
    agent,
  };
}

async function cleanupTestData() {
  // Delete in correct order due to foreign key constraints
  await prisma.user.deleteMany({
    where: {
      email: {
        in: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ],
      },
    },
  });

  await prisma.organization.deleteMany({
    where: {
      slug: {
        in: ['api-test-org', 'api-test-new-org'],
      },
    },
  });
}
