
# Properties API Documentation

## Overview
The Properties API provides comprehensive property management functionality for the CRM system. This API allows you to create, read, update, delete, and search properties within your organization.

## Base URL
```
/api/properties
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Permission Requirements
- **Read operations**: All authenticated users can view properties
- **Write operations**: Require `AGENT` role or higher (CREATE, UPDATE, DELETE, SEARCH)

## Endpoints

### 1. Get All Properties
**GET** `/api/properties`

Retrieve a paginated list of properties for your organization.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Items per page (default: 20, max: 100) |
| `search` | string | No | Search in address, city, zipCode, country, description |
| `status` | PropertyStatus | No | Filter by property status |
| `type` | PropertyType | No | Filter by property type |
| `minPrice` | number | No | Minimum price |
| `maxPrice` | number | No | Maximum price |
| `minBedrooms` | number | No | Minimum number of bedrooms |
| `maxBedrooms` | number | No | Maximum number of bedrooms |
| `minBathrooms` | number | No | Minimum number of bathrooms |
| `maxBathrooms` | number | No | Maximum number of bathrooms |
| `minSquareFootage` | number | No | Minimum square footage |
| `maxSquareFootage` | number | No | Maximum square footage |
| `yearBuiltMin` | number | No | Minimum year built |
| `yearBuiltMax` | number | No | Maximum year built |
| `parking` | boolean | No | Filter by parking availability |
| `pool` | boolean | No | Filter by pool availability |
| `gym` | boolean | No | Filter by gym availability |
| `sortBy` | string | No | Sort field (default: createdAt) |
| `sortOrder` | 'asc' \| 'desc' | No | Sort order (default: desc) |

#### Response
```json
{
  "success": true,
  "data": {
    "properties": [
      {
        "id": "cuid",
        "address": "123 Main St",
        "city": "Anytown",
        "state": "CA",
        "zipCode": "90210",
        "country": "US",
        "price": 500000,
        "bedrooms": 3,
        "bathrooms": 2.5,
        "squareFootage": 1800,
        "lotSize": 7000,
        "yearBuilt": 1990,
        "propertyType": "SINGLE_FAMILY",
        "status": "ACTIVE",
        "description": "Beautiful family home",
        "amenities": ["POOL", "GYM"],
        "parkingSpaces": 2,
        "ownerId": "user-cuid",
        "organizationId": "org-cuid",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z",
        "isDeleted": false
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. Get Property by ID
**GET** `/api/properties/:id`

Retrieve a specific property by ID.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Property ID |

#### Response
```json
{
  "success": true,
  "data": {
    "id": "cuid",
    "address": "123 Main St",
    // ... full property object
  }
}
```

### 3. Create Property
**POST** `/api/properties`

Create a new property. Requires `AGENT` role or higher.

#### Request Body
```json
{
  "address": "123 Main St",
  "city": "Anytown",
  "state": "CA",
  "zipCode": "90210",
  "country": "US",
  "price": 500000,
  "bedrooms": 3,
  "bathrooms": 2.5,
  "squareFootage": 1800,
  "lotSize": 7000,
  "yearBuilt": 1990,
  "propertyType": "SINGLE_FAMILY",
  "status": "ACTIVE",
  "description": "Beautiful family home with a spacious yard.",
  "amenities": ["POOL", "GYM"],
  "parkingSpaces": 2
}
```

#### Required Fields
- `address` (string)
- `city` (string)
- `state` (string)
- `zipCode` (string)
- `country` (string)
- `price` (number)
- `bedrooms` (number)
- `bathrooms` (number)
- `squareFootage` (number)
- `propertyType` (PropertyType)
- `status` (PropertyStatus)

#### Response
```json
{
  "success": true,
  "data": {
    "id": "newly-created-cuid",
    "address": "123 Main St",
    // ... full property object
  },
  "message": "Property created successfully"
}
```

### 4. Update Property
**PUT** `/api/properties/:id`

Update an existing property. Requires `AGENT` role or higher.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Property ID |

#### Request Body
Any subset of the property fields (partial update):
```json
{
  "price": 520000,
  "status": "SOLD",
  "description": "Updated description with new details."
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "id": "cuid",
    "price": 520000,
    // ... updated property object
  },
  "message": "Property updated successfully"
}
```

### 5. Delete Property
**DELETE** `/api/properties/:id`

Soft delete a property. Requires `AGENT` role or higher.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Property ID |

#### Response
```json
{
  "success": true,
  "data": null,
  "message": "Property deleted successfully"
}
```

### 6. Search Properties
**GET** `/api/properties/search`

Advanced search for properties. Requires `AGENT` role or higher.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | Yes | Search query | 
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Items per page (default: 20, max: 100) |
| `status` | PropertyStatus | No | Filter by status |
| `type` | PropertyType | No | Filter by type |
| `minPrice` | number | No | Minimum price |
| `maxPrice` | number | No | Maximum price |
| `minBedrooms` | number | No | Minimum number of bedrooms |
| `maxBedrooms` | number | No | Maximum number of bedrooms |
| `minBathrooms` | number | No | Minimum number of bathrooms |
| `maxBathrooms` | number | No | Maximum number of bathrooms |
| `minSquareFootage` | number | No | Minimum square footage |
| `maxSquareFootage` | number | No | Maximum square footage |
| `yearBuiltMin` | number | No | Minimum year built |
| `yearBuiltMax` | number | No | Maximum year built |
| `parking` | boolean | No | Filter by parking availability |
| `pool` | boolean | No | Filter by pool availability |
| `gym` | boolean | No | Filter by gym availability |

#### Response
Same format as "Get All Properties" endpoint.

## Data Types and Enums

### PropertyType
```typescript
enum PropertyType {
  SINGLE_FAMILY = "SINGLE_FAMILY",
  CONDO = "CONDO",
  TOWNHOUSE = "TOWNHOUSE",
  MULTI_FAMILY = "MULTI_FAMILY",
  LAND = "LAND",
  COMMERCIAL = "COMMERCIAL",
  INDUSTRIAL = "INDUSTRIAL",
  RENTAL = "RENTAL",
  APARTMENT = "APARTMENT",
  OTHER = "OTHER"
}
```

### PropertyStatus
```typescript
enum PropertyStatus {
  ACTIVE = "ACTIVE",
  PENDING = "PENDING",
  SOLD = "SOLD",
  RENTED = "RENTED",
  OFF_MARKET = "OFF_MARKET",
  COMING_SOON = "COMING_SOON"
}
```

### Amenities
```typescript
enum Amenity {
  POOL = "POOL",
  GYM = "GYM",
  PARKING = "PARKING",
  BALCONY = "BALCONY",
  GARDEN = "GARDEN",
  LAUNDRY = "LAUNDRY",
  FIREPLACE = "FIREPLACE",
  CENTRAL_AIR = "CENTRAL_AIR",
  HARDWOOD_FLOORS = "HARDWOOD_FLOORS",
  WATERFRONT = "WATERFRONT",
  GOLF_COURSE_NEARBY = "GOLF_COURSE_NEARBY",
  MOUNTAIN_VIEW = "MOUNTAIN_VIEW",
  CITY_VIEW = "CITY_VIEW"
}
```

### Property Object Structure
```typescript
interface Property {
  id: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string; // 2-letter country code
  price: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFootage?: number;
  lotSize?: number;
  yearBuilt?: number;
  propertyType: PropertyType;
  status: PropertyStatus;
  description?: string;
  amenities?: Amenity[];
  parkingSpaces?: number;
  ownerId: string; // User ID who owns this property listing
  organizationId: string; // Organization ID this property belongs to
  createdAt: string; // ISO 8601 date
  updatedAt: string; // ISO 8601 date
  isDeleted: boolean;
}
```

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "statusCode": 400
}
```

### Common Error Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (property doesn't exist)
- `500` - Internal Server Error

### Common Error Messages
- "Authentication required"
- "Access denied to this property"
- "Property not found"
- "Search query is required"
- "Agent role required"

## Frontend Usage Examples

### React Hook for Properties API
```typescript
import { useState, useEffect } from 'react';

interface UsePropertiesProps {
  page?: number;
  limit?: number;
  search?: string;
  status?: PropertyStatus;
  type?: PropertyType;
}

export const useProperties = (params: UsePropertiesProps = {}) => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [pagination, setPagination] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProperties();
  }, [params]);

  const fetchProperties = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/properties?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch properties');
      }

      const data = await response.json();
      setProperties(data.data.properties);
      setPagination(data.data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return { properties, pagination, loading, error, refetch: fetchProperties };
};
```

### Creating a Property
```typescript
const createProperty = async (propertyData: CreatePropertyInput) => {
  try {
    const response = await fetch('/api/properties', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(propertyData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create property');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error creating property:', error);
    throw error;
  }
};
```

### Updating a Property
```typescript
const updateProperty = async (propertyId: string, updates: Partial<Property>) => {
  try {
    const response = await fetch(`/api/properties/${propertyId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update property');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error updating property:', error);
    throw error;
  }
};
```

### Searching Properties
```typescript
const searchProperties = async (query: string, filters: SearchFilters = {}) => {
  try {
    const queryParams = new URLSearchParams({
      query,
      ...filters
    });

    const response = await fetch(`/api/properties/search?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to search properties');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error searching properties:', error);
    throw error;
  }
};
```

## Best Practices

### 1. Authentication
- Always include the JWT token in requests
- Handle token expiration gracefully
- Implement automatic token refresh

### 2. Error Handling
- Check response status codes
- Display user-friendly error messages
- Log detailed errors for debugging

### 3. Data Validation
- Validate data on the frontend before sending
- Use the same validation rules as the backend
- Provide real-time validation feedback

### 4. Performance
- Implement pagination for large datasets
- Use debouncing for search inputs
- Cache frequently accessed data

### 5. User Experience
- Show loading states during API calls
- Provide success/error feedback
- Implement optimistic updates where appropriate

### 6. Security
- Never expose sensitive data in URLs
- Validate permissions before showing UI elements
- Sanitize user inputs

## Rate Limiting
The API implements rate limiting to prevent abuse. If you exceed the rate limit, you'll receive a `429 Too Many Requests` response.

## Data Privacy
- All property data is scoped to the user's organization
- Personal data should be handled according to privacy regulations

## Support
For questions or issues with the Properties API, please contact the backend development team or refer to the main API documentation. 