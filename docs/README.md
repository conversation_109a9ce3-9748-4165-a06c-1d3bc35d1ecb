# Real Estate CRM API Documentation

This directory contains comprehensive HTTP API documentation for the Real Estate CRM microservices. The documentation is provided in `.http` format, which can be executed directly in VS Code with the REST Client extension or similar tools.

## 📁 Documentation Files

### [auth.http](./auth.http)
Complete documentation for the **Authentication Service** endpoints:
- User login/logout
- Token refresh mechanism
- User registration (admin only)
- Password management
- Current user information
- Role-based access control examples

### [organizations.http](./organizations.http)
Complete documentation for the **Organizations Service** endpoints:
- Organization CRUD operations
- Multi-tenant access controls
- Role-based permissions
- Organization management workflows

## 🚀 Getting Started

### Prerequisites
1. **VS Code** with the **REST Client** extension installed
2. **Running CRM services**:
   ```bash
   npm run dev:all
   ```
3. **Database seeded** with test data:
   ```bash
   npm run db:seed
   ```

### Using the Documentation

1. **Open any `.http` file** in VS Code
2. **Update environment variables** at the top of each file if needed
3. **Click "Send Request"** above any HTTP request to execute it
4. **Follow the authentication flow**:
   - Start with login requests in `auth.http`
   - Copy access tokens to use in other requests
   - Use the tokens in `organizations.http` requests

## 🔐 Authentication Flow

### Step 1: Login
```http
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

### Step 2: Copy Access Token
From the login response, copy the `accessToken` value.

### Step 3: Use Token in Requests
```http
GET http://localhost:3000/api/organizations
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
```

### Step 4: Refresh When Expired
```http
POST http://localhost:3000/api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "YOUR_REFRESH_TOKEN_HERE"
}
```

## 🌐 Environment Configuration

### Development (Default)
```
@baseUrl = http://localhost:3000
@authServiceUrl = http://localhost:3001
@organizationsServiceUrl = http://localhost:3003
```

### Staging
```
@baseUrl = https://api-staging.yourcrm.com
@authServiceUrl = https://auth-staging.yourcrm.com
@organizationsServiceUrl = https://organizations-staging.yourcrm.com
```

### Production
```
@baseUrl = https://api.yourcrm.com
@authServiceUrl = https://auth.yourcrm.com
@organizationsServiceUrl = https://organizations.yourcrm.com
```

## 👥 Test Credentials

The following test accounts are available after running `npm run db:seed`:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| SUPER_ADMIN | <EMAIL> | admin123 | Full system access |
| ADMIN | <EMAIL> | admin123 | Organization admin |
| AGENT | <EMAIL> | admin123 | Regular user |

## 🔒 Role-Based Access Control

### Authentication Service
- **Public**: Login, Refresh Token
- **Authenticated**: Logout, Get Current User, Change Password
- **Admin+**: User Registration

### Organizations Service
- **SUPER_ADMIN**: All operations, can create organizations
- **ADMIN**: Can update/delete their organization
- **MANAGER/AGENT/VIEWER**: Read-only access to their organization

## 🏢 Multi-Tenant Architecture

### Organization Isolation
- Users can only access data from their organization
- Organization ID is embedded in JWT tokens
- All API requests validate organization membership
- Cross-organization access is prevented

### Data Security
- Soft delete preserves data integrity
- Audit trails for all operations
- Input validation on all endpoints
- Rate limiting and security headers

## 📊 API Response Format

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation successful",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint"
}
```

### Error Response
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint"
}
```

### Validation Error Response
```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "\"email\" must be a valid email"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint"
}
```

## 🔧 Service Architecture

### API Gateway (Port 3000)
- Routes requests to microservices
- Handles CORS, security headers, rate limiting
- Provides unified API endpoint

### Authentication Service (Port 3001)
- JWT token management
- User authentication and authorization
- Password management
- User registration

### Organizations Service (Port 3003)
- Organization CRUD operations
- Multi-tenant data management
- Organization settings and configuration

## 📝 Request/Response Examples

Each `.http` file contains:
- ✅ **Complete request examples** with headers and body
- ✅ **Expected response examples** for success cases
- ✅ **Error response examples** for various failure scenarios
- ✅ **Validation error examples** with detailed field errors
- ✅ **Authentication flow demonstrations**
- ✅ **Role-based access control examples**

## 🛠️ Development Tips

### Using Variables
The `.http` files use variables for reusability:
```http
@baseUrl = http://localhost:3000
@accessToken = your_token_here

GET {{baseUrl}}/api/organizations
Authorization: Bearer {{accessToken}}
```

### Testing Different Roles
1. Login with different user accounts
2. Copy their respective access tokens
3. Test the same endpoints with different roles
4. Observe different responses based on permissions

### Error Testing
Each endpoint includes examples of:
- Invalid authentication
- Insufficient permissions
- Validation errors
- Resource not found
- Conflict scenarios

## 📚 Additional Resources

- [Project README](../README.md) - Main project documentation
- [Implementation Summary](../IMPLEMENTATION_SUMMARY.md) - Technical details
- [Project Status](../PROJECT_STATUS.md) - Current development status

## 🤝 Contributing

When adding new endpoints:
1. Add requests to the appropriate `.http` file
2. Include success and error response examples
3. Document required authentication and permissions
4. Add validation error examples
5. Update this README if needed

## 📞 Support

For questions about the API documentation:
1. Check the inline comments in `.http` files
2. Review the expected response examples
3. Verify your authentication tokens are valid
4. Ensure services are running on correct ports
