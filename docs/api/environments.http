###
# Real Estate CRM - Environment Configurations
# 
# This file contains environment-specific configurations for different deployment stages.
# Copy the appropriate section to your main .http files or use VS Code REST Client settings.
###

###
# DEVELOPMENT ENVIRONMENT
# Local development with all services running on localhost
###

# Development Variables
@dev_baseUrl = http://localhost:3000
@dev_authServiceUrl = http://localhost:3001
@dev_organizationsServiceUrl = http://localhost:3003
@dev_contentType = application/json

# Development Test Credentials
@dev_superAdminEmail = <EMAIL>
@dev_adminEmail = <EMAIL>
@dev_agentEmail = <EMAIL>
@dev_password = admin123

### Development Health Checks
GET {{dev_baseUrl}}/health

###
GET {{dev_authServiceUrl}}/health

###
GET {{dev_organizationsServiceUrl}}/health

### Development Login Test
POST {{dev_baseUrl}}/api/auth/login
Content-Type: {{dev_contentType}}

{
  "email": "{{dev_superAdminEmail}}",
  "password": "{{dev_password}}"
}

###
# STAGING ENVIRONMENT
# Pre-production environment for testing
###

# Staging Variables
@staging_baseUrl = https://api-staging.yourcrm.com
@staging_authServiceUrl = https://auth-staging.yourcrm.com
@staging_organizationsServiceUrl = https://organizations-staging.yourcrm.com
@staging_contentType = application/json

# Staging Test Credentials (update these based on staging data)
@staging_superAdminEmail = <EMAIL>
@staging_adminEmail = <EMAIL>
@staging_agentEmail = <EMAIL>
@staging_password = staging_password_here

### Staging Health Checks
GET {{staging_baseUrl}}/health

###
GET {{staging_authServiceUrl}}/health

###
GET {{staging_organizationsServiceUrl}}/health

### Staging Login Test
POST {{staging_baseUrl}}/api/auth/login
Content-Type: {{staging_contentType}}

{
  "email": "{{staging_superAdminEmail}}",
  "password": "{{staging_password}}"
}

###
# PRODUCTION ENVIRONMENT
# Live production environment - USE WITH CAUTION
###

# Production Variables
@prod_baseUrl = https://api.yourcrm.com
@prod_authServiceUrl = https://auth.yourcrm.com
@prod_organizationsServiceUrl = https://organizations.yourcrm.com
@prod_contentType = application/json

# Production Credentials (NEVER commit real production credentials)
@prod_testEmail = <EMAIL>
@prod_testPassword = secure_production_password

### Production Health Checks (Safe to run)
GET {{prod_baseUrl}}/health

###
GET {{prod_authServiceUrl}}/health

###
GET {{prod_organizationsServiceUrl}}/health

### Production API Documentation (Safe to run)
GET {{prod_baseUrl}}/api

### Production Login Test (Use test account only)
# POST {{prod_baseUrl}}/api/auth/login
# Content-Type: {{prod_contentType}}
# 
# {
#   "email": "{{prod_testEmail}}",
#   "password": "{{prod_testPassword}}"
# }

###
# LOCAL TESTING ENVIRONMENT
# For testing individual services without API Gateway
###

# Direct Service URLs
@local_authService = http://localhost:3001
@local_organizationsService = http://localhost:3003

### Test Auth Service Directly
POST {{local_authService}}/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

### Test Organizations Service Directly (requires auth token)
GET {{local_organizationsService}}/
Authorization: Bearer your_token_here

###
# DOCKER ENVIRONMENT
# When running services in Docker containers
###

# Docker Variables (adjust ports if different)
@docker_baseUrl = http://localhost:3000
@docker_authServiceUrl = http://localhost:3001
@docker_organizationsServiceUrl = http://localhost:3003

### Docker Health Checks
GET {{docker_baseUrl}}/health

###
GET {{docker_authServiceUrl}}/health

###
GET {{docker_organizationsServiceUrl}}/health

###
# KUBERNETES ENVIRONMENT
# When services are deployed in Kubernetes
###

# Kubernetes Variables (update with your cluster URLs)
@k8s_baseUrl = https://api.k8s.yourcrm.com
@k8s_authServiceUrl = https://auth.k8s.yourcrm.com
@k8s_organizationsServiceUrl = https://organizations.k8s.yourcrm.com

### Kubernetes Health Checks
GET {{k8s_baseUrl}}/health

###
GET {{k8s_authServiceUrl}}/health

###
GET {{k8s_organizationsServiceUrl}}/health

###
# ENVIRONMENT-SPECIFIC CONFIGURATIONS
###

### Development Configuration
# - CORS enabled for localhost
# - Detailed error messages
# - Debug logging enabled
# - Rate limiting relaxed
# - Test data available

### Staging Configuration  
# - Production-like setup
# - Limited test data
# - Moderate rate limiting
# - Error logging enabled
# - SSL/TLS required

### Production Configuration
# - Strict security settings
# - Minimal error details
# - Full rate limiting
# - Comprehensive logging
# - SSL/TLS required
# - Monitoring enabled

###
# SWITCHING BETWEEN ENVIRONMENTS
###

# Method 1: Update variables at the top of each .http file
# @baseUrl = http://localhost:3000          # Development
# @baseUrl = https://api-staging.yourcrm.com # Staging  
# @baseUrl = https://api.yourcrm.com         # Production

# Method 2: Use VS Code REST Client settings
# Create .vscode/settings.json with:
# {
#   "rest-client.environmentVariables": {
#     "development": {
#       "baseUrl": "http://localhost:3000",
#       "authServiceUrl": "http://localhost:3001"
#     },
#     "staging": {
#       "baseUrl": "https://api-staging.yourcrm.com",
#       "authServiceUrl": "https://auth-staging.yourcrm.com"
#     },
#     "production": {
#       "baseUrl": "https://api.yourcrm.com",
#       "authServiceUrl": "https://auth.yourcrm.com"
#     }
#   }
# }

###
# SECURITY CONSIDERATIONS
###

### Development
# - Use test credentials only
# - Local database with sample data
# - No real user information
# - Safe to experiment

### Staging
# - Use staging-specific test accounts
# - Limited real-like data
# - Coordinate with team for testing
# - Document any data changes

### Production
# - NEVER use real user credentials for testing
# - Create dedicated test accounts
# - Minimal testing on production
# - Always coordinate with team
# - Log all production API testing

###
# TROUBLESHOOTING BY ENVIRONMENT
###

### Development Issues
# - Check if all services are running: npm run dev:all
# - Verify database is seeded: npm run db:seed
# - Check service logs in terminal
# - Ensure ports 3000, 3001, 3003 are available

### Staging Issues
# - Check deployment status
# - Verify SSL certificates
# - Check environment variables
# - Review staging logs

### Production Issues
# - Check monitoring dashboards
# - Review error logs
# - Verify service health endpoints
# - Coordinate with DevOps team

###
# ENVIRONMENT VALIDATION TESTS
###

### Quick Environment Validation
# Run these tests to verify environment is working:

### 1. Health Check
GET {{dev_baseUrl}}/health

### 2. API Documentation
GET {{dev_baseUrl}}/api

### 3. Authentication Test
POST {{dev_baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

### 4. Protected Endpoint Test (use token from step 3)
GET {{dev_baseUrl}}/api/auth/me
Authorization: Bearer paste_token_here

### 5. Service Communication Test
GET {{dev_baseUrl}}/api/organizations
Authorization: Bearer paste_token_here

###
# NOTES FOR DIFFERENT ENVIRONMENTS
###

# Development:
# - All services run locally
# - Hot reloading enabled
# - Detailed error messages
# - Test data available

# Staging:
# - Deployed to staging infrastructure
# - Production-like configuration
# - Limited test data
# - SSL/TLS enabled

# Production:
# - Live user data
# - High availability setup
# - Monitoring and alerting
# - Backup and disaster recovery

###
