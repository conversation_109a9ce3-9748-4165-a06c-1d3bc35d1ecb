###
# Real Estate CRM - Authentication Service API Documentation
# 
# This file contains HTTP requests for testing the Authentication microservice endpoints.
# Use with VS Code REST Client extension or similar tools.
#
# Base URL: {{baseUrl}}/api/auth
# Service Port: 3001 (direct) | 3000 (via API Gateway)
###

# Environment Variables
@baseUrl = http://localhost:3000
@authServiceUrl = http://localhost:3001
@contentType = application/json

# Test credentials (from seed data)
@superAdminEmail = <EMAIL>
@adminEmail = <EMAIL>
@agentEmail = <EMAIL>
@testPassword = admin123

# Variables for storing tokens (will be set after login)
@accessToken = 
@refreshToken = 

###
# 1. USER LOGIN
# POST /api/auth/login
# Public endpoint - no authentication required
# Returns user data with access and refresh tokens
###

POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{superAdminEmail}}",
  "password": "{{testPassword}}"
}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "user": {
#       "id": "user-id",
#       "email": "<EMAIL>",
#       "firstName": "Super",
#       "lastName": "Admin",
#       "role": "SUPER_ADMIN",
#       "isActive": true,
#       "organizationId": "org-id",
#       "organization": {
#         "id": "org-id",
#         "name": "ACME Real Estate",
#         "slug": "acme-realestate",
#         "isActive": true
#       }
#     },
#     "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
#   },
#   "message": "Login successful",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/login"
# }

###
# Login as Admin User
###

POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{adminEmail}}",
  "password": "{{testPassword}}"
}

###
# Login as Agent User
###

POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{agentEmail}}",
  "password": "{{testPassword}}"
}

###
# Login Error Cases
###

# Invalid credentials
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

### Expected Response (401 Unauthorized):
# {
#   "success": false,
#   "error": "UNAUTHORIZED",
#   "message": "Invalid email or password",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/login"
# }

# Missing password
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>"
}

### Expected Response (422 Unprocessable Entity):
# {
#   "success": false,
#   "error": "VALIDATION_ERROR",
#   "message": "Validation failed",
#   "details": [
#     {
#       "field": "password",
#       "message": "\"password\" is required"
#     }
#   ],
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/login"
# }

###
# 2. GET CURRENT USER
# GET /api/auth/me
# Requires: Bearer token authentication
# Returns current user information with organization data
###

GET {{baseUrl}}/api/auth/me
Authorization: Bearer {{accessToken}}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "id": "user-id",
#     "email": "<EMAIL>",
#     "firstName": "Super",
#     "lastName": "Admin",
#     "role": "SUPER_ADMIN",
#     "isActive": true,
#     "organizationId": "org-id",
#     "createdAt": "2024-01-01T00:00:00.000Z",
#     "updatedAt": "2024-01-01T00:00:00.000Z",
#     "organization": {
#       "id": "org-id",
#       "name": "ACME Real Estate",
#       "slug": "acme-realestate",
#       "isActive": true
#     }
#   },
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/me"
# }

###
# Get Current User - Unauthorized
###

GET {{baseUrl}}/api/auth/me

### Expected Response (401 Unauthorized):
# {
#   "success": false,
#   "error": "UNAUTHORIZED",
#   "message": "Access token required",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/me"
# }

###
# 3. REFRESH TOKEN
# POST /api/auth/refresh
# Public endpoint - requires refresh token in body
# Returns new access and refresh tokens
###

POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "{{refreshToken}}"
}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
#   },
#   "message": "Token refreshed successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/refresh"
# }

###
# Refresh Token - Invalid Token
###

POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "invalid.refresh.token"
}

### Expected Response (401 Unauthorized):
# {
#   "success": false,
#   "error": "UNAUTHORIZED",
#   "message": "Invalid refresh token",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/refresh"
# }

###
# 4. USER LOGOUT
# POST /api/auth/logout
# Requires: Bearer token authentication
# Blacklists the current access token
###

POST {{baseUrl}}/api/auth/logout
Authorization: Bearer {{accessToken}}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": null,
#   "message": "Logout successful",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/logout"
# }

###
# 5. CHANGE PASSWORD
# PUT /api/auth/change-password
# Requires: Bearer token authentication
# Changes the current user's password
###

PUT {{baseUrl}}/api/auth/change-password
Authorization: Bearer {{accessToken}}
Content-Type: {{contentType}}

{
  "currentPassword": "{{testPassword}}",
  "newPassword": "newSecurePassword123"
}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": null,
#   "message": "Password changed successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/change-password"
# }

###
# Change Password - Wrong Current Password
###

PUT {{baseUrl}}/api/auth/change-password
Authorization: Bearer {{accessToken}}
Content-Type: {{contentType}}

{
  "currentPassword": "wrongCurrentPassword",
  "newPassword": "newSecurePassword123"
}

### Expected Response (400 Bad Request):
# {
#   "success": false,
#   "error": "BAD_REQUEST",
#   "message": "Current password is incorrect",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/change-password"
# }

###
# 6. REGISTER NEW USER
# POST /api/auth/register
# Requires: Bearer token authentication + ADMIN or SUPER_ADMIN role
# Creates a new user account
###

POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{accessToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "New",
  "lastName": "User",
  "organizationId": "org-id-here",
  "role": "AGENT"
}

### Expected Response (201 Created):
# {
#   "success": true,
#   "data": {
#     "id": "new-user-id",
#     "email": "<EMAIL>",
#     "firstName": "New",
#     "lastName": "User",
#     "role": "AGENT",
#     "isActive": true,
#     "organizationId": "org-id-here",
#     "organization": {
#       "id": "org-id-here",
#       "name": "ACME Real Estate",
#       "slug": "acme-realestate",
#       "isActive": true
#     }
#   },
#   "message": "User registered successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/register"
# }

###
# Register User - Duplicate Email
###

POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{accessToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "Duplicate",
  "lastName": "User",
  "organizationId": "org-id-here",
  "role": "AGENT"
}

### Expected Response (409 Conflict):
# {
#   "success": false,
#   "error": "CONFLICT",
#   "message": "User with this email already exists",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/register"
# }

###
# Register User - Insufficient Permissions (Agent trying to register)
###

POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{agentAccessToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "Unauthorized",
  "lastName": "User",
  "organizationId": "org-id-here",
  "role": "AGENT"
}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Insufficient permissions",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/register"
# }

###
# Register User - Validation Error
###

POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{accessToken}}
Content-Type: {{contentType}}

{
  "email": "invalid-email",
  "password": "123",
  "firstName": "A",
  "lastName": "",
  "organizationId": "",
  "role": "INVALID_ROLE"
}

### Expected Response (422 Unprocessable Entity):
# {
#   "success": false,
#   "error": "VALIDATION_ERROR",
#   "message": "Validation failed",
#   "details": [
#     {
#       "field": "email",
#       "message": "\"email\" must be a valid email"
#     },
#     {
#       "field": "password",
#       "message": "\"password\" length must be at least 8 characters long"
#     },
#     {
#       "field": "firstName",
#       "message": "\"firstName\" length must be at least 2 characters long"
#     },
#     {
#       "field": "lastName",
#       "message": "\"lastName\" is not allowed to be empty"
#     },
#     {
#       "field": "organizationId",
#       "message": "\"organizationId\" is not allowed to be empty"
#     },
#     {
#       "field": "role",
#       "message": "\"role\" must be one of [ADMIN, MANAGER, AGENT, VIEWER]"
#     }
#   ],
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/auth/register"
# }

###
# AUTHENTICATION FLOW EXAMPLE
#
# 1. Login to get tokens
# 2. Use access token for authenticated requests
# 3. Refresh token when access token expires
# 4. Logout to invalidate tokens
###

### Step 1: Login
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{superAdminEmail}}",
  "password": "{{testPassword}}"
}

### Step 2: Use the access token from login response for authenticated requests
### Copy the accessToken from the login response and use it in subsequent requests

### Step 3: When access token expires (15 minutes), use refresh token
POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "paste_refresh_token_here"
}

### Step 4: Logout when done
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer paste_access_token_here

###
# DIRECT SERVICE ACCESS (bypassing API Gateway)
# Use these endpoints when testing the auth service directly
###

### Login directly to auth service
POST {{authServiceUrl}}/login
Content-Type: {{contentType}}

{
  "email": "{{superAdminEmail}}",
  "password": "{{testPassword}}"
}

### Get current user directly from auth service
GET {{authServiceUrl}}/me
Authorization: Bearer {{accessToken}}

###
# ENVIRONMENT CONFIGURATIONS
#
# Development:
# @baseUrl = http://localhost:3000
# @authServiceUrl = http://localhost:3001
#
# Staging:
# @baseUrl = https://api-staging.yourcrm.com
# @authServiceUrl = https://auth-staging.yourcrm.com
#
# Production:
# @baseUrl = https://api.yourcrm.com
# @authServiceUrl = https://auth.yourcrm.com
###

###
# ROLE-BASED ACCESS CONTROL
#
# SUPER_ADMIN: Can access all endpoints, create organizations, register users
# ADMIN: Can access organization endpoints, register users within their org
# MANAGER: Can access organization data, limited user management
# AGENT: Can access organization data, no user management
# VIEWER: Read-only access to organization data
###

###
# MULTI-TENANT ORGANIZATION ISOLATION
#
# All authenticated requests automatically enforce organization isolation:
# - Users can only access data from their own organization
# - Organization ID is embedded in JWT token
# - Middleware validates organization access on every request
# - Cross-organization data access is prevented
###
