###
# Real Estate CRM - Organizations Service API Documentation
# 
# This file contains HTTP requests for testing the Organizations microservice endpoints.
# Use with VS Code REST Client extension or similar tools.
#
# Base URL: {{baseUrl}}/api/organizations
# Service Port: 3003 (direct) | 3000 (via API Gateway)
###

# Environment Variables
@baseUrl = http://localhost:3000
@organizationsServiceUrl = http://localhost:3003
@contentType = application/json

# Test credentials (from seed data)
@superAdminEmail = <EMAIL>
@adminEmail = <EMAIL>
@agentEmail = <EMAIL>
@testPassword = admin123

# Variables for storing tokens (get these from auth.http login responses)
@superAdminToken = 
@adminToken = 
@agentToken = 

# Test organization data
@testOrgId = 
@testOrgSlug = test-organization

###
# PREREQUISITE: LOGIN TO GET ACCESS TOKENS
# Run the login requests from auth.http first to get access tokens
###

###
# 1. GET USER'S ORGANIZATIONS
# GET /api/organizations
# Requires: Bearer token authentication
# Returns organizations that the user belongs to (enforces organization isolation)
###

GET {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": [
#     {
#       "id": "org-id",
#       "name": "ACME Real Estate",
#       "slug": "acme-realestate",
#       "domain": "acme-realestate.com",
#       "logo": null,
#       "address": "123 Main St, City, State 12345",
#       "phone": "******-0123",
#       "email": "<EMAIL>",
#       "website": "https://acme-realestate.com",
#       "timezone": "America/New_York",
#       "currency": "USD",
#       "subscriptionPlan": "PROFESSIONAL",
#       "subscriptionStatus": "ACTIVE",
#       "trialEndsAt": null,
#       "subscriptionEndsAt": "2024-12-31T23:59:59.000Z",
#       "maxUsers": 50,
#       "maxProperties": 1000,
#       "settings": {},
#       "isActive": true,
#       "isDeleted": false,
#       "deletedAt": null,
#       "createdAt": "2024-01-01T00:00:00.000Z",
#       "updatedAt": "2024-01-01T00:00:00.000Z"
#     }
#   ],
#   "message": "Organizations fetched successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# Get Organizations as Admin User
###

GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
# Get Organizations as Agent User
###

GET {{baseUrl}}/api/organizations
Authorization: Bearer {{agentToken}}

###
# Get Organizations - Unauthorized
###

GET {{baseUrl}}/api/organizations

### Expected Response (401 Unauthorized):
# {
#   "success": false,
#   "error": "UNAUTHORIZED",
#   "message": "Access token required",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# 2. GET SPECIFIC ORGANIZATION
# GET /api/organizations/:id
# Requires: Bearer token authentication + user must belong to the organization
# Returns detailed information about a specific organization
###

GET {{baseUrl}}/api/organizations/{{testOrgId}}
Authorization: Bearer {{adminToken}}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "id": "org-id",
#     "name": "ACME Real Estate",
#     "slug": "acme-realestate",
#     "domain": "acme-realestate.com",
#     "logo": null,
#     "address": "123 Main St, City, State 12345",
#     "phone": "******-0123",
#     "email": "<EMAIL>",
#     "website": "https://acme-realestate.com",
#     "timezone": "America/New_York",
#     "currency": "USD",
#     "subscriptionPlan": "PROFESSIONAL",
#     "subscriptionStatus": "ACTIVE",
#     "trialEndsAt": null,
#     "subscriptionEndsAt": "2024-12-31T23:59:59.000Z",
#     "maxUsers": 50,
#     "maxProperties": 1000,
#     "settings": {},
#     "isActive": true,
#     "isDeleted": false,
#     "deletedAt": null,
#     "createdAt": "2024-01-01T00:00:00.000Z",
#     "updatedAt": "2024-01-01T00:00:00.000Z"
#   },
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/org-id"
# }

###
# Get Organization - Access Denied (user from different organization)
###

GET {{baseUrl}}/api/organizations/different-org-id
Authorization: Bearer {{adminToken}}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Access denied to this organization",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/different-org-id"
# }

###
# Get Organization - Not Found
###

GET {{baseUrl}}/api/organizations/non-existent-id
Authorization: Bearer {{adminToken}}

### Expected Response (404 Not Found):
# {
#   "success": false,
#   "error": "NOT_FOUND",
#   "message": "Organization not found",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/non-existent-id"
# }

###
# 3. CREATE ORGANIZATION
# POST /api/organizations
# Requires: Bearer token authentication + SUPER_ADMIN role
# Creates a new organization
###

POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "New Real Estate Company",
  "slug": "new-real-estate-co",
  "domain": "newrealestate.com",
  "logo": "https://example.com/logo.png",
  "address": "456 Business Ave, City, State 67890",
  "phone": "******-9876",
  "email": "<EMAIL>",
  "website": "https://newrealestate.com",
  "timezone": "America/Los_Angeles",
  "currency": "USD",
  "settings": {
    "theme": "dark",
    "notifications": {
      "email": true,
      "sms": false
    }
  }
}

### Expected Response (201 Created):
# {
#   "success": true,
#   "data": {
#     "id": "new-org-id",
#     "name": "New Real Estate Company",
#     "slug": "new-real-estate-co",
#     "domain": "newrealestate.com",
#     "logo": "https://example.com/logo.png",
#     "address": "456 Business Ave, City, State 67890",
#     "phone": "******-9876",
#     "email": "<EMAIL>",
#     "website": "https://newrealestate.com",
#     "timezone": "America/Los_Angeles",
#     "currency": "USD",
#     "subscriptionPlan": "STARTER",
#     "subscriptionStatus": "TRIAL",
#     "trialEndsAt": "2024-02-01T00:00:00.000Z",
#     "subscriptionEndsAt": null,
#     "maxUsers": 5,
#     "maxProperties": 100,
#     "settings": {
#       "theme": "dark",
#       "notifications": {
#         "email": true,
#         "sms": false
#       }
#     },
#     "isActive": true,
#     "isDeleted": false,
#     "deletedAt": null,
#     "createdAt": "2024-01-01T00:00:00.000Z",
#     "updatedAt": "2024-01-01T00:00:00.000Z"
#   },
#   "message": "Organization created successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# Create Organization - Minimal Required Fields
###

POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Minimal Organization",
  "slug": "minimal-org"
}

###
# Create Organization - Insufficient Permissions (Admin trying to create)
###

POST {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}
Content-Type: {{contentType}}

{
  "name": "Unauthorized Organization",
  "slug": "unauthorized-org"
}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Only super admins can create organizations",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# Create Organization - Duplicate Slug
###

POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Duplicate Slug Organization",
  "slug": "acme-realestate"
}

### Expected Response (409 Conflict):
# {
#   "success": false,
#   "error": "CONFLICT",
#   "message": "Organization slug already exists",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# Create Organization - Validation Error
###

POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "A",
  "slug": "invalid slug with spaces",
  "domain": "invalid-domain",
  "email": "invalid-email",
  "website": "not-a-url",
  "currency": "INVALID"
}

### Expected Response (422 Unprocessable Entity):
# {
#   "success": false,
#   "error": "VALIDATION_ERROR",
#   "message": "Validation failed",
#   "details": [
#     {
#       "field": "name",
#       "message": "\"name\" length must be at least 2 characters long"
#     },
#     {
#       "field": "slug",
#       "message": "\"slug\" with value \"invalid slug with spaces\" fails to match the required pattern: /^[a-z0-9-]+$/"
#     },
#     {
#       "field": "domain",
#       "message": "\"domain\" must be a valid domain"
#     },
#     {
#       "field": "email",
#       "message": "\"email\" must be a valid email"
#     },
#     {
#       "field": "website",
#       "message": "\"website\" must be a valid uri"
#     },
#     {
#       "field": "currency",
#       "message": "\"currency\" length must be 3 characters long"
#     }
#   ],
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations"
# }

###
# 4. UPDATE ORGANIZATION
# PUT /api/organizations/:id
# Requires: Bearer token authentication + ADMIN or SUPER_ADMIN role + user must belong to organization
# Updates an existing organization
###

PUT {{baseUrl}}/api/organizations/{{testOrgId}}
Authorization: Bearer {{adminToken}}
Content-Type: {{contentType}}

{
  "name": "Updated ACME Real Estate",
  "address": "789 Updated Street, New City, State 54321",
  "phone": "******-1111",
  "email": "<EMAIL>",
  "website": "https://updated.acme-realestate.com",
  "timezone": "America/Chicago",
  "settings": {
    "theme": "light",
    "notifications": {
      "email": true,
      "sms": true,
      "push": true
    },
    "features": {
      "advancedReporting": true,
      "apiAccess": true
    }
  }
}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": {
#     "id": "org-id",
#     "name": "Updated ACME Real Estate",
#     "slug": "acme-realestate",
#     "domain": "acme-realestate.com",
#     "logo": null,
#     "address": "789 Updated Street, New City, State 54321",
#     "phone": "******-1111",
#     "email": "<EMAIL>",
#     "website": "https://updated.acme-realestate.com",
#     "timezone": "America/Chicago",
#     "currency": "USD",
#     "subscriptionPlan": "PROFESSIONAL",
#     "subscriptionStatus": "ACTIVE",
#     "trialEndsAt": null,
#     "subscriptionEndsAt": "2024-12-31T23:59:59.000Z",
#     "maxUsers": 50,
#     "maxProperties": 1000,
#     "settings": {
#       "theme": "light",
#       "notifications": {
#         "email": true,
#         "sms": true,
#         "push": true
#       },
#       "features": {
#         "advancedReporting": true,
#         "apiAccess": true
#       }
#     },
#     "isActive": true,
#     "isDeleted": false,
#     "deletedAt": null,
#     "createdAt": "2024-01-01T00:00:00.000Z",
#     "updatedAt": "2024-01-01T12:00:00.000Z"
#   },
#   "message": "Organization updated successfully",
#   "timestamp": "2024-01-01T12:00:00.000Z",
#   "path": "/api/organizations/org-id"
# }

###
# Update Organization - Insufficient Permissions (Agent trying to update)
###

PUT {{baseUrl}}/api/organizations/{{testOrgId}}
Authorization: Bearer {{agentToken}}
Content-Type: {{contentType}}

{
  "name": "Unauthorized Update"
}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Insufficient permissions to update organization",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/org-id"
# }

###
# 5. DELETE ORGANIZATION
# DELETE /api/organizations/:id
# Requires: Bearer token authentication + ADMIN or SUPER_ADMIN role + user must belong to organization
# Soft deletes an organization (sets isDeleted=true, deletedAt=timestamp)
###

DELETE {{baseUrl}}/api/organizations/{{testOrgId}}
Authorization: Bearer {{adminToken}}

### Expected Response (200 OK):
# {
#   "success": true,
#   "data": null,
#   "message": "Organization deleted successfully",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/org-id"
# }

###
# Delete Organization - Insufficient Permissions (Agent trying to delete)
###

DELETE {{baseUrl}}/api/organizations/{{testOrgId}}
Authorization: Bearer {{agentToken}}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Insufficient permissions to delete organization",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/org-id"
# }

###
# Delete Organization - Access Denied (user from different organization)
###

DELETE {{baseUrl}}/api/organizations/different-org-id
Authorization: Bearer {{adminToken}}

### Expected Response (403 Forbidden):
# {
#   "success": false,
#   "error": "FORBIDDEN",
#   "message": "Access denied to this organization",
#   "timestamp": "2024-01-01T00:00:00.000Z",
#   "path": "/api/organizations/different-org-id"
# }

###
# DIRECT SERVICE ACCESS (bypassing API Gateway)
# Use these endpoints when testing the organizations service directly
###

### Get organizations directly from organizations service
GET {{organizationsServiceUrl}}/
Authorization: Bearer {{adminToken}}

### Create organization directly via organizations service
POST {{organizationsServiceUrl}}/
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Direct Service Test Org",
  "slug": "direct-service-test"
}

###
# ORGANIZATION WORKFLOW EXAMPLES
###

### Complete Organization Management Workflow
### 1. Login as Super Admin (from auth.http)
### 2. Create a new organization
### 3. Login as Admin of that organization
### 4. Update organization details
### 5. Get organization information
### 6. Delete organization (if needed)

### Step 1: Create Organization (Super Admin only)
POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Workflow Test Organization",
  "slug": "workflow-test-org",
  "email": "<EMAIL>",
  "timezone": "America/New_York",
  "currency": "USD"
}

### Step 2: Get the created organization
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}

### Step 3: Update organization (use organization ID from step 2)
PUT {{baseUrl}}/api/organizations/org-id-from-step-2
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Updated Workflow Test Organization",
  "address": "123 Workflow Street, Test City, TC 12345",
  "phone": "******-WORKFLOW"
}

### Step 4: Get specific organization details
GET {{baseUrl}}/api/organizations/org-id-from-step-2
Authorization: Bearer {{superAdminToken}}

###
# ENVIRONMENT CONFIGURATIONS
#
# Development:
# @baseUrl = http://localhost:3000
# @organizationsServiceUrl = http://localhost:3003
#
# Staging:
# @baseUrl = https://api-staging.yourcrm.com
# @organizationsServiceUrl = https://organizations-staging.yourcrm.com
#
# Production:
# @baseUrl = https://api.yourcrm.com
# @organizationsServiceUrl = https://organizations.yourcrm.com
###

###
# ROLE-BASED ACCESS CONTROL FOR ORGANIZATIONS
#
# SUPER_ADMIN:
#   - Can create new organizations
#   - Can access, update, and delete any organization
#   - Full administrative privileges
#
# ADMIN:
#   - Can update and delete their own organization
#   - Can view their organization details
#   - Cannot create new organizations
#   - Cannot access other organizations
#
# MANAGER/AGENT/VIEWER:
#   - Can only view their organization details
#   - Cannot update or delete organizations
#   - Cannot create new organizations
#   - Cannot access other organizations
###

###
# MULTI-TENANT ORGANIZATION ISOLATION
#
# Security Features:
# - Organization ID is embedded in JWT token
# - All requests validate user belongs to requested organization
# - Cross-organization data access is prevented
# - Soft delete preserves data integrity
# - Audit trail maintained for all operations
#
# Data Isolation:
# - Users can only see their organization's data
# - API automatically filters by organization ID
# - Database queries include organization constraints
# - No cross-tenant data leakage possible
###
