{"name": "@crm/organizations-service", "version": "1.0.0", "description": "Organizations microservice for Real Estate CRM", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "clean": "rm -rf dist"}, "dependencies": {"@crm/database": "*", "@crm/shared": "*", "dotenv": "^16.3.0", "express": "^4.18.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "jest": "^29.6.0", "jest-mock-extended": "^4.0.0-beta1", "nodemon": "^3.0.0", "supertest": "^7.1.1", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}