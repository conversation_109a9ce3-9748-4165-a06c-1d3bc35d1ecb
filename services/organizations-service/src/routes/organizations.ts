import { Router } from 'express';
import {
  validateBody,
  validateParams,
  createOrganizationSchema,
  updateOrganizationSchema,
  idSchema,
  authenticateToken,
  asyncHandler,
} from '@crm/shared';
import * as organizationController from '../controllers/organizationController';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// GET /organizations - Get user's organizations
router.get('/', asyncHandler(organizationController.getOrganizations));

// GET /organizations/:id - Get specific organization
router.get(
  '/:id',
  validateParams(idSchema),
  asyncHandler(organizationController.getOrganizationById)
);

// POST /organizations - Create new organization (SUPER_ADMIN only)
router.post(
  '/',
  validateBody(createOrganizationSchema),
  asyncHandler(organizationController.createOrganization)
);

// PUT /organizations/:id - Update organization (ADMIN only)
router.put(
  '/:id',
  validateParams(idSchema),
  validateBody(updateOrganizationSchema),
  asyncHandler(organizationController.updateOrganization)
);

// DELETE /organizations/:id - Delete organization (ADMIN only)
router.delete(
  '/:id',
  validateParams(idSchema),
  asyncHandler(organizationController.deleteOrganization)
);

export default router;
