import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  generalLimiter,
  createServiceLogger,
} from '@crm/shared';
import organizationRoutes from './routes/organizations';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const PORT = process.env.ORGANIZATIONS_SERVICE_PORT || 3003;
const logger = createServiceLogger('organizations-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(generalLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'organizations-service',
    version: '1.0.0',
  });
});

// Routes
app.use('/', organizationRoutes);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🏢 Organizations Service running on port ${PORT}`);
});

export default app;
