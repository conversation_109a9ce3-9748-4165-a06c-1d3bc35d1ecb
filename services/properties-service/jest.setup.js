// Jest setup for properties service
require('dotenv').config({ path: '../../.env' });

// Mock Prisma client for unit tests
jest.mock('@crm/database', () => ({
  prisma: {
    property: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  },
}));
