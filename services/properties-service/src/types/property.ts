import { PropertyType, PropertyStatus } from '@prisma/client';

export interface CreatePropertyInput {
  title: string;
  description?: string;
  propertyType: PropertyType;
  status?: PropertyStatus;
  
  // Location
  address: string;
  city: string;
  state: string;
  zipCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  
  // Property details
  bedrooms?: number;
  bathrooms?: number;
  squareMeters?: number;
  lotSizeMeters?: number;
  yearBuilt?: number;
  
  // Pricing
  listPrice?: number;
  salePrice?: number;
  rentPrice?: number;
  pricePerSquareMeter?: number;
  
  // Assignment
  assigneeId?: string;
  
  // Features & amenities
  features?: string[];
  amenities?: string[];
  
  // Media
  images?: string[];
  virtualTourUrl?: string;
  
  // Listing details
  mlsNumber?: string;
  listingDate?: Date;
  expirationDate?: Date;
  daysOnMarket?: number;
  
  // Status
  isFeatured?: boolean;
  
  // Multi-tenant
  organizationId: string;
}

export type UpdatePropertyInput = Partial<Omit<CreatePropertyInput, 'organizationId'>>;

export interface PropertyFilterParams {
  organizationId: string;
  page?: number;
  limit?: number;
  search?: string;
  
  // Location filters
  city?: string;
  state?: string;
  zipCode?: string;
  
  // Property filters
  propertyType?: PropertyType;
  status?: PropertyStatus;
  assigneeId?: string;
  
  // Size filters
  minSquareMeters?: number;
  maxSquareMeters?: number;
  minLotSize?: number;
  maxLotSize?: number;
  
  // Bedroom/bathroom filters
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  
  // Price filters
  minPrice?: number;
  maxPrice?: number;
  priceType?: 'listPrice' | 'salePrice' | 'rentPrice';
  
  // Date filters
  listedAfter?: Date;
  listedBefore?: Date;
  updatedAfter?: Date;
  updatedBefore?: Date;
  
  // Feature filters
  features?: string[];
  amenities?: string[];
  
  // Status filters
  isFeatured?: boolean;
  isActive?: boolean;
  
  // Sorting
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PropertySearchParams {
  query: string;
  organizationId: string;
  page?: number;
  limit?: number;
  propertyType?: PropertyType;
  status?: PropertyStatus;
  minPrice?: number;
  maxPrice?: number;
  city?: string;
  state?: string;
}
