import { Router } from 'express';
import {
  validateBody,
  authenticateToken,
  asyncHandler,
  requireAgent,
  validateQuery,
} from '@crm/shared';
import * as PropertyController from '../controllers/propertyController';
import { 
  createPropertySchema, 
  getPropertiesQuerySchema, 
  updatePropertySchema, 
  searchPropertiesQuerySchema 
} from '../utils/validation/propertySchema';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// GET /properties - List properties with comprehensive filtering
router.get(
  '/',
  validateQuery(getPropertiesQuerySchema),
  asyncHandler(PropertyController.getAllProperties)
);

// POST /properties - Create new property
router.post(
  '/',
  requireAgent,
  validateBody(createPropertySchema),
  asyncHandler(PropertyController.createProperty)
);

// GET /properties/search - Advanced search
router.get(
  '/search',
  requireAgent,
  validateQuery(searchPropertiesQuerySchema),
  asyncHandler(PropertyController.searchProperties)
);

// GET /properties/:id - Get property by ID
router.get(
  '/:id',
  asyncHandler(PropertyController.getPropertyById)
);

// PUT /properties/:id - Update property
router.put(
  '/:id',
  requireAgent,
  validateBody(updatePropertySchema),
  asyncHandler(PropertyController.updateProperty)
);

// DELETE /properties/:id - Delete property (soft delete)
router.delete(
  '/:id',
  requireAgent,
  asyncHandler(PropertyController.deleteProperty)
);

export default router;
