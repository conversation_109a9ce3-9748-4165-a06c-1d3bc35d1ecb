import { prisma } from '@crm/database';
import { ServiceResponse, createServiceLogger } from '@crm/shared';
import { Property, Prisma } from '@prisma/client';
import { CreatePropertyInput, UpdatePropertyInput, PropertyFilterParams, PropertySearchParams } from '../types/property';

const logger = createServiceLogger('properties-service');

export const PropertyService = {

  // Create a new property
  async createProperty(input: CreatePropertyInput): Promise<ServiceResponse<Property>> {
    try {
      const { organizationId, assigneeId, ...data } = input;

      // Check if assignee exists and belongs to the same organization
      if (assigneeId) {
        const assignee = await prisma.user.findFirst({
          where: {
            id: assigneeId,
            organizationId,
            isActive: true,
            isDeleted: false,
          },
        });

        if (!assignee) {
          return {
            success: false,
            message: 'Assignee not found or does not belong to your organization',
            statusCode: 400,
          };
        }
      }

      const property = await prisma.property.create({
        data: {
          ...data,
          organization: { connect: { id: organizationId } },
          ...(assigneeId && { assignee: { connect: { id: assigneeId } } }),
        },
        include: {
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return {
        success: true,
        data: property,
      };
    } catch (error) {
      logger.error('Create property error:', error);
      return {
        success: false,
        message: 'Failed to create property',
        statusCode: 500,
      };
    }
  },

  // Get property by ID
  async getPropertyById(id: string, organizationId: string): Promise<ServiceResponse<Property>> {
    try {
      const property = await prisma.property.findFirst({
        where: {
          id,
          organizationId,
          isDeleted: false,
        },
        include: {
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!property) {
        return {
          success: false,
          message: 'Property not found',
          statusCode: 404,
        };
      }

      return {
        success: true,
        data: property,
      };
    } catch (error) {
      logger.error('Get property by ID error:', error);
      return {
        success: false,
        message: 'Failed to fetch property',
        statusCode: 500,
      };
    }
  },

  // Update property
  async updateProperty(
    id: string,
    organizationId: string,
    input: UpdatePropertyInput
  ): Promise<ServiceResponse<Property>> {
    try {
      // Check if property exists and belongs to organization
      const existingProperty = await prisma.property.findFirst({
        where: {
          id,
          organizationId,
          isDeleted: false,
        },
      });

      if (!existingProperty) {
        return {
          success: false,
          message: 'Property not found',
          statusCode: 404,
        };
      }

      // Check if assignee exists and belongs to the same organization
      if (input.assigneeId) {
        const assignee = await prisma.user.findFirst({
          where: {
            id: input.assigneeId,
            organizationId,
            isActive: true,
            isDeleted: false,
          },
        });

        if (!assignee) {
          return {
            success: false,
            message: 'Assignee not found or does not belong to your organization',
            statusCode: 400,
          };
        }
      }

      const { assigneeId, ...data } = input;

      const property = await prisma.property.update({
        where: { id },
        data: {
          ...data,
          ...(assigneeId !== undefined && {
            assignee: assigneeId ? { connect: { id: assigneeId } } : { disconnect: true },
          }),
        },
        include: {
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return {
        success: true,
        data: property,
      };
    } catch (error) {
      logger.error('Update property error:', error);
      return {
        success: false,
        message: 'Failed to update property',
        statusCode: 500,
      };
    }
  },

  // Get properties with comprehensive filtering
  async getProperties(
    filterParams: PropertyFilterParams
  ): Promise<ServiceResponse<{ properties: Property[]; pagination: any }>> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        city,
        state,
        zipCode,
        propertyType,
        status,
        assigneeId,
        minSquareMeters,
        maxSquareMeters,
        minLotSize,
        maxLotSize,
        minBedrooms,
        maxBedrooms,
        minBathrooms,
        maxBathrooms,
        minPrice,
        maxPrice,
        priceType = 'listPrice',
        listedAfter,
        listedBefore,
        updatedAfter,
        updatedBefore,
        features,
        amenities,
        isFeatured,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filterParams;

      // Ensure limit doesn't exceed 100
      const validatedLimit = Math.min(limit, 100);
      const skip = (page - 1) * validatedLimit;

      // Build where clause
      const where: Prisma.PropertyWhereInput = {
        isDeleted: false,
        organizationId: filterParams.organizationId,
      };

      // Location filters
      if (city) where.city = { contains: city, mode: 'insensitive' };
      if (state) where.state = { contains: state, mode: 'insensitive' };
      if (zipCode) where.zipCode = { contains: zipCode, mode: 'insensitive' };

      // Property type and status filters
      if (propertyType) where.propertyType = propertyType;
      if (status) where.status = status;
      if (assigneeId) where.assigneeId = assigneeId;

      // Size filters
      if (minSquareMeters || maxSquareMeters) {
        where.squareMeters = {};
        if (minSquareMeters) where.squareMeters.gte = minSquareMeters;
        if (maxSquareMeters) where.squareMeters.lte = maxSquareMeters;
      }

      if (minLotSize || maxLotSize) {
        where.lotSizeMeters = {};
        if (minLotSize) where.lotSizeMeters.gte = minLotSize;
        if (maxLotSize) where.lotSizeMeters.lte = maxLotSize;
      }

      // Bedroom/bathroom filters
      if (minBedrooms || maxBedrooms) {
        where.bedrooms = {};
        if (minBedrooms) where.bedrooms.gte = minBedrooms;
        if (maxBedrooms) where.bedrooms.lte = maxBedrooms;
      }

      if (minBathrooms || maxBathrooms) {
        where.bathrooms = {};
        if (minBathrooms) where.bathrooms.gte = minBathrooms;
        if (maxBathrooms) where.bathrooms.lte = maxBathrooms;
      }

      // Price filters
      if (minPrice || maxPrice) {
        const priceField = priceType as keyof Pick<Prisma.PropertyWhereInput, 'listPrice' | 'salePrice' | 'rentPrice'>;
        where[priceField] = {};
        if (minPrice) (where[priceField] as any).gte = minPrice;
        if (maxPrice) (where[priceField] as any).lte = maxPrice;
      }

      // Date filters
      if (listedAfter || listedBefore) {
        where.listingDate = {};
        if (listedAfter) where.listingDate.gte = listedAfter;
        if (listedBefore) where.listingDate.lte = listedBefore;
      }

      if (updatedAfter || updatedBefore) {
        where.updatedAt = {};
        if (updatedAfter) where.updatedAt.gte = updatedAfter;
        if (updatedBefore) where.updatedAt.lte = updatedBefore;
      }

      // Feature filters
      if (features && features.length > 0) {
        where.features = { hasEvery: features };
      }

      if (amenities && amenities.length > 0) {
        where.amenities = { hasEvery: amenities };
      }

      // Status filters
      if (isFeatured !== undefined) where.isFeatured = isFeatured;
      if (isActive !== undefined) where.isActive = isActive;

      // Search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { address: { contains: search, mode: 'insensitive' } },
          { city: { contains: search, mode: 'insensitive' } },
          { state: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Build orderBy clause
      const orderBy: Prisma.PropertyOrderByWithRelationInput = {};

      switch (sortBy) {
        case 'price':
          orderBy[priceType as keyof Prisma.PropertyOrderByWithRelationInput] = sortOrder;
          break;
        case 'size':
          orderBy.squareMeters = sortOrder;
          break;
        case 'bedrooms':
          orderBy.bedrooms = sortOrder;
          break;
        case 'bathrooms':
          orderBy.bathrooms = sortOrder;
          break;
        case 'listingDate':
          orderBy.listingDate = sortOrder;
          break;
        case 'updatedAt':
          orderBy.updatedAt = sortOrder;
          break;
        default:
          orderBy.createdAt = sortOrder;
      }

      const [properties, total] = await Promise.all([
        prisma.property.findMany({
          where,
          skip,
          take: validatedLimit,
          orderBy,
          include: {
            assignee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        }),
        prisma.property.count({ where }),
      ]);

      return {
        success: true,
        data: {
          properties,
          pagination: {
            page,
            limit: validatedLimit,
            total,
            totalPages: Math.ceil(total / validatedLimit),
            hasNext: page * validatedLimit < total,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      logger.error('Get properties error:', error);
      return {
        success: false,
        message: 'Failed to fetch properties',
        statusCode: 500,
      };
    }
  },

  // Advanced search properties
  async searchProperties(
    searchParams: PropertySearchParams
  ): Promise<ServiceResponse<{ properties: Property[]; pagination: any }>> {
    try {
      const {
        query,
        page = 1,
        limit = 20,
        propertyType,
        status,
        minPrice,
        maxPrice,
        city,
        state,
      } = searchParams;

      const validatedLimit = Math.min(limit, 100);
      const skip = (page - 1) * validatedLimit;

      // Build comprehensive search filters
      const where: Prisma.PropertyWhereInput = {
        isDeleted: false,
        organizationId: searchParams.organizationId,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { address: { contains: query, mode: 'insensitive' } },
          { city: { contains: query, mode: 'insensitive' } },
          { state: { contains: query, mode: 'insensitive' } },
          { zipCode: { contains: query, mode: 'insensitive' } },
          { mlsNumber: { contains: query, mode: 'insensitive' } },
          { features: { has: query } },
          { amenities: { has: query } },
        ],
      };

      // Apply additional filters
      if (propertyType) where.propertyType = propertyType;
      if (status) where.status = status;
      if (city) where.city = { contains: city, mode: 'insensitive' };
      if (state) where.state = { contains: state, mode: 'insensitive' };

      // Price range filter
      if (minPrice || maxPrice) {
        where.OR = where.OR?.map(condition => ({
          AND: [
            condition,
            {
              OR: [
                ...(minPrice || maxPrice ? [{
                  listPrice: {
                    ...(minPrice && { gte: minPrice }),
                    ...(maxPrice && { lte: maxPrice }),
                  }
                }] : []),
                ...(minPrice || maxPrice ? [{
                  salePrice: {
                    ...(minPrice && { gte: minPrice }),
                    ...(maxPrice && { lte: maxPrice }),
                  }
                }] : []),
                ...(minPrice || maxPrice ? [{
                  rentPrice: {
                    ...(minPrice && { gte: minPrice }),
                    ...(maxPrice && { lte: maxPrice }),
                  }
                }] : []),
              ]
            }
          ]
        }));
      }

      const [properties, total] = await Promise.all([
        prisma.property.findMany({
          where,
          skip,
          take: validatedLimit,
          orderBy: { createdAt: 'desc' },
          include: {
            assignee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        }),
        prisma.property.count({ where }),
      ]);

      return {
        success: true,
        data: {
          properties,
          pagination: {
            page,
            limit: validatedLimit,
            total,
            totalPages: Math.ceil(total / validatedLimit),
            hasNext: page * validatedLimit < total,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      logger.error('Search properties error:', error);
      return {
        success: false,
        message: 'Failed to search properties',
        statusCode: 500,
      };
    }
  },

  // Delete property (soft delete)
  async deleteProperty(id: string, organizationId: string): Promise<ServiceResponse<void>> {
    try {
      const existingProperty = await prisma.property.findFirst({
        where: {
          id,
          organizationId,
          isDeleted: false,
        },
      });

      if (!existingProperty) {
        return {
          success: false,
          message: 'Property not found',
          statusCode: 404,
        };
      }

      await prisma.property.update({
        where: { id },
        data: {
          isDeleted: true,
          deletedAt: new Date(),
        },
      });

      return {
        success: true,
      };
    } catch (error) {
      logger.error('Delete property error:', error);
      return {
        success: false,
        message: 'Failed to delete property',
        statusCode: 500,
      };
    }
  },
};
