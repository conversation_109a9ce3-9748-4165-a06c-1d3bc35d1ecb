import request from 'supertest';
import app from '../../index';
import { testDb, setupTestData, cleanupTestData } from './setup';
import { JWTHelper } from '@crm/shared';
import { PropertyType, PropertyStatus } from '@prisma/client';

describe('Properties Integration Tests', () => {
  let testData: any;
  let agentToken: string;
  let managerToken: string;
  let anotherAgentToken: string;

  beforeAll(async () => {
    testData = await setupTestData();

    // Generate tokens for different users
    agentToken = JWTHelper.generateTokenPair({
      userId: testData.agent.id,
      email: testData.agent.email,
      role: testData.agent.role,
      organizationId: testData.agent.organizationId,
    }).accessToken;

    managerToken = JWTHelper.generateTokenPair({
      userId: testData.manager.id,
      email: testData.manager.email,
      role: testData.manager.role,
      organizationId: testData.manager.organizationId,
    }).accessToken;

    anotherAgentToken = JWTHelper.generateTokenPair({
      userId: testData.anotherAgent.id,
      email: testData.anotherAgent.email,
      role: testData.anotherAgent.role,
      organizationId: testData.anotherAgent.organizationId,
    }).accessToken;
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('GET /v1/properties', () => {
    it('should successfully get properties for organization', async () => {
      const response = await request(app)
        .get('/v1/properties')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(3);
      expect(response.body.data.pagination).toMatchObject({
        page: 1,
        limit: 20,
        total: 3,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });

      // Check that properties belong to the correct organization
      response.body.data.properties.forEach((property: any) => {
        expect(property.organizationId).toBe(testData.organization1.id);
      });
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/v1/properties')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should return different properties for different organization', async () => {
      const response = await request(app)
        .get('/v1/properties')
        .set('Authorization', `Bearer ${anotherAgentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0]).toMatchObject({
        title: 'Another Org Property',
        organizationId: testData.organization2.id,
      });
    });

    it('should filter properties by city', async () => {
      const response = await request(app)
        .get('/v1/properties?city=Springfield')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(2);
      response.body.data.properties.forEach((property: any) => {
        expect(property.city).toBe('Springfield');
      });
    });

    it('should filter properties by property type', async () => {
      const response = await request(app)
        .get(`/v1/properties?propertyType=${PropertyType.CONDO}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].propertyType).toBe(PropertyType.CONDO);
    });

    it('should filter properties by status', async () => {
      const response = await request(app)
        .get(`/v1/properties?status=${PropertyStatus.AVAILABLE}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(2);
      response.body.data.properties.forEach((property: any) => {
        expect(property.status).toBe(PropertyStatus.AVAILABLE);
      });
    });

    it('should filter properties by price range', async () => {
      const response = await request(app)
        .get('/v1/properties?minPrice=300000&maxPrice=400000')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].listPrice).toBe(350000);
    });

    it('should filter properties by bedrooms', async () => {
      const response = await request(app)
        .get('/v1/properties?minBedrooms=3&maxBedrooms=3')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].bedrooms).toBe(3);
    });

    it('should filter properties by features', async () => {
      const response = await request(app)
        .get('/v1/properties?features=garage')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(2);
      response.body.data.properties.forEach((property: any) => {
        expect(property.features).toContain('garage');
      });
    });

    it('should filter properties by featured status', async () => {
      const response = await request(app)
        .get('/v1/properties?isFeatured=true')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(2);
      response.body.data.properties.forEach((property: any) => {
        expect(property.isFeatured).toBe(true);
      });
    });

    it('should search properties by text', async () => {
      const response = await request(app)
        .get('/v1/properties?search=downtown')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].description).toContain('downtown');
    });

    it('should sort properties by price', async () => {
      const response = await request(app)
        .get('/v1/properties?sortBy=price&sortOrder=asc')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      const prices = response.body.data.properties.map((p: any) => p.listPrice);
      expect(prices).toEqual([280000, 350000, 450000]);
    });

    it('should paginate results', async () => {
      const response = await request(app)
        .get('/v1/properties?page=1&limit=2')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(2);
      expect(response.body.data.pagination).toMatchObject({
        page: 1,
        limit: 2,
        total: 3,
        totalPages: 2,
        hasNext: true,
        hasPrev: false,
      });
    });
  });

  describe('GET /v1/properties/:id', () => {
    it('should successfully get property by ID', async () => {
      const response = await request(app)
        .get(`/v1/properties/${testData.property1.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testData.property1.id,
        title: 'Beautiful Family Home',
        propertyType: PropertyType.SINGLE_FAMILY,
        status: PropertyStatus.AVAILABLE,
        city: 'Springfield',
        bedrooms: 3,
        bathrooms: 2.5,
        listPrice: 350000,
      });
      expect(response.body.data.assignee).toMatchObject({
        id: testData.agent.id,
        firstName: 'Test',
        lastName: 'Agent',
      });
    });

    it('should return 404 for non-existent property', async () => {
      const response = await request(app)
        .get('/v1/properties/non-existent-id')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Property not found');
    });

    it('should not allow access to property from different organization', async () => {
      const response = await request(app)
        .get(`/v1/properties/${testData.propertyFromAnotherOrg.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /v1/properties', () => {
    it('should successfully create a new property', async () => {
      const newProperty = {
        title: 'New Test Property',
        description: 'A newly created test property',
        propertyType: PropertyType.SINGLE_FAMILY,
        status: PropertyStatus.AVAILABLE,
        address: '999 Test Street',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        bedrooms: 3,
        bathrooms: 2,
        squareMeters: 150,
        listPrice: 300000,
        features: ['garage', 'garden'],
        amenities: ['pool'],
        assigneeId: testData.agent.id,
      };

      const response = await request(app)
        .post('/v1/properties')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(newProperty)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        title: 'New Test Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        city: 'Test City',
        bedrooms: 3,
        listPrice: 300000,
        organizationId: testData.organization1.id,
      });
      expect(response.body.data.assignee).toMatchObject({
        id: testData.agent.id,
      });
    });

    it('should fail to create property with invalid assignee', async () => {
      const newProperty = {
        title: 'Invalid Assignee Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        address: '999 Invalid Street',
        city: 'Invalid City',
        state: 'IV',
        assigneeId: testData.anotherAgent.id, // From different organization
      };

      const response = await request(app)
        .post('/v1/properties')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(newProperty)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Assignee not found');
    });

    it('should fail to create property with missing required fields', async () => {
      const invalidProperty = {
        description: 'Missing required fields',
      };

      const response = await request(app)
        .post('/v1/properties')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(invalidProperty)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should fail without agent role', async () => {
      // This would require a viewer token, but for now we test with agent
      const newProperty = {
        title: 'Unauthorized Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        address: '999 Unauthorized Street',
        city: 'Unauthorized City',
        state: 'UN',
      };

      const response = await request(app)
        .post('/v1/properties')
        .send(newProperty)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /v1/properties/:id', () => {
    it('should successfully update a property', async () => {
      const updateData = {
        title: 'Updated Property Title',
        description: 'Updated description',
        listPrice: 375000,
        bedrooms: 4,
        features: ['garage', 'garden', 'pool'],
      };

      const response = await request(app)
        .put(`/v1/properties/${testData.property1.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testData.property1.id,
        title: 'Updated Property Title',
        description: 'Updated description',
        listPrice: 375000,
        bedrooms: 4,
        features: ['garage', 'garden', 'pool'],
      });
    });

    it('should successfully update property assignee', async () => {
      const updateData = {
        assigneeId: testData.manager.id,
      };

      const response = await request(app)
        .put(`/v1/properties/${testData.property1.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.assignee).toMatchObject({
        id: testData.manager.id,
        firstName: 'Test',
        lastName: 'Manager',
      });
    });

    it('should successfully remove property assignee', async () => {
      const updateData = {
        assigneeId: null,
      };

      const response = await request(app)
        .put(`/v1/properties/${testData.property1.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.assignee).toBeNull();
    });

    it('should return 404 for non-existent property', async () => {
      const updateData = {
        title: 'Updated Title',
      };

      const response = await request(app)
        .put('/v1/properties/non-existent-id')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Property not found');
    });

    it('should fail to update with invalid assignee', async () => {
      const updateData = {
        assigneeId: testData.anotherAgent.id, // From different organization
      };

      const response = await request(app)
        .put(`/v1/properties/${testData.property1.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Assignee not found');
    });

    it('should not allow updating property from different organization', async () => {
      const updateData = {
        title: 'Unauthorized Update',
      };

      const response = await request(app)
        .put(`/v1/properties/${testData.propertyFromAnotherOrg.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /v1/properties/:id', () => {
    it('should successfully delete a property', async () => {
      const response = await request(app)
        .delete(`/v1/properties/${testData.property2.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Property deleted successfully');

      // Verify property is soft deleted
      const deletedProperty = await testDb.property.findUnique({
        where: { id: testData.property2.id },
      });
      expect(deletedProperty?.isDeleted).toBe(true);
      expect(deletedProperty?.deletedAt).toBeTruthy();
    });

    it('should return 404 for non-existent property', async () => {
      const response = await request(app)
        .delete('/v1/properties/non-existent-id')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Property not found');
    });

    it('should not allow deleting property from different organization', async () => {
      const response = await request(app)
        .delete(`/v1/properties/${testData.propertyFromAnotherOrg.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should return 404 when trying to delete already deleted property', async () => {
      // First delete
      await request(app)
        .delete(`/v1/properties/${testData.property3.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      // Try to delete again
      const response = await request(app)
        .delete(`/v1/properties/${testData.property3.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Property not found');
    });
  });

  describe('GET /v1/properties/search', () => {
    it('should successfully search properties by query', async () => {
      const response = await request(app)
        .get('/v1/properties/search?query=family')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].title).toContain('Family');
    });

    it('should search properties by address', async () => {
      const response = await request(app)
        .get('/v1/properties/search?query=Main Street')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].address).toContain('Main Street');
    });

    it('should search properties by MLS number', async () => {
      const response = await request(app)
        .get('/v1/properties/search?query=MLS123456')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toHaveLength(1);
      expect(response.body.data.properties[0].mlsNumber).toBe('MLS123456');
    });

    it('should fail without search query', async () => {
      const response = await request(app)
        .get('/v1/properties/search')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Search query is required');
    });

    it('should only return properties from user organization', async () => {
      const response = await request(app)
        .get('/v1/properties/search?query=Property')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.properties.forEach((property: any) => {
        expect(property.organizationId).toBe(testData.organization1.id);
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle invalid property ID format gracefully', async () => {
      const response = await request(app)
        .get('/v1/properties/invalid-id-format')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should enforce maximum limit for pagination', async () => {
      const response = await request(app)
        .get('/v1/properties?limit=1000')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.limit).toBeLessThanOrEqual(100);
    });
  });
});
