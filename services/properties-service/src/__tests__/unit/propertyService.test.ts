import { PropertyService } from '../../services/propertyService';
import { prisma } from '@crm/database';
import { PropertyType, PropertyStatus } from '@prisma/client';

// Mock the prisma client
jest.mock('@crm/database');
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('PropertyService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createProperty', () => {
    it('should successfully create a property', async () => {
      const mockProperty = {
        id: 'property-1',
        title: 'Test Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        status: PropertyStatus.AVAILABLE,
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        organizationId: 'org-1',
        assigneeId: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockAssignee = {
        id: 'user-1',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        organizationId: 'org-1',
        isActive: true,
        isDeleted: false,
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockAssignee as any);
      mockPrisma.property.create.mockResolvedValue(mockProperty as any);

      const input = {
        title: 'Test Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        organizationId: 'org-1',
        assigneeId: 'user-1',
      };

      const result = await PropertyService.createProperty(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockProperty);
      expect(mockPrisma.user.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'user-1',
          organizationId: 'org-1',
          isActive: true,
          isDeleted: false,
        },
      });
    });

    it('should fail when assignee does not exist', async () => {
      mockPrisma.user.findFirst.mockResolvedValue(null);

      const input = {
        title: 'Test Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        organizationId: 'org-1',
        assigneeId: 'invalid-user',
      };

      const result = await PropertyService.createProperty(input);

      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('Assignee not found');
    });

    it('should handle database errors', async () => {
      mockPrisma.user.findFirst.mockRejectedValue(new Error('Database error'));

      const input = {
        title: 'Test Property',
        propertyType: PropertyType.SINGLE_FAMILY,
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        organizationId: 'org-1',
      };

      const result = await PropertyService.createProperty(input);

      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(500);
      expect(result.message).toBe('Failed to create property');
    });
  });

  describe('getPropertyById', () => {
    it('should successfully get property by ID', async () => {
      const mockProperty = {
        id: 'property-1',
        title: 'Test Property',
        organizationId: 'org-1',
        isDeleted: false,
      };

      mockPrisma.property.findFirst.mockResolvedValue(mockProperty as any);

      const result = await PropertyService.getPropertyById('property-1', 'org-1');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockProperty);
      expect(mockPrisma.property.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'property-1',
          organizationId: 'org-1',
          isDeleted: false,
        },
        include: {
          assignee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });
    });

    it('should return 404 when property not found', async () => {
      mockPrisma.property.findFirst.mockResolvedValue(null);

      const result = await PropertyService.getPropertyById('non-existent', 'org-1');

      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(404);
      expect(result.message).toBe('Property not found');
    });
  });

  describe('updateProperty', () => {
    it('should successfully update a property', async () => {
      const existingProperty = {
        id: 'property-1',
        organizationId: 'org-1',
        isDeleted: false,
      };

      const updatedProperty = {
        ...existingProperty,
        title: 'Updated Property',
      };

      mockPrisma.property.findFirst.mockResolvedValue(existingProperty as any);
      mockPrisma.property.update.mockResolvedValue(updatedProperty as any);

      const result = await PropertyService.updateProperty('property-1', 'org-1', {
        title: 'Updated Property',
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(updatedProperty);
    });

    it('should return 404 when property not found', async () => {
      mockPrisma.property.findFirst.mockResolvedValue(null);

      const result = await PropertyService.updateProperty('non-existent', 'org-1', {
        title: 'Updated Property',
      });

      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(404);
      expect(result.message).toBe('Property not found');
    });
  });

  describe('deleteProperty', () => {
    it('should successfully soft delete a property', async () => {
      const existingProperty = {
        id: 'property-1',
        organizationId: 'org-1',
        isDeleted: false,
      };

      mockPrisma.property.findFirst.mockResolvedValue(existingProperty as any);
      mockPrisma.property.update.mockResolvedValue({} as any);

      const result = await PropertyService.deleteProperty('property-1', 'org-1');

      expect(result.success).toBe(true);
      expect(mockPrisma.property.update).toHaveBeenCalledWith({
        where: { id: 'property-1' },
        data: {
          isDeleted: true,
          deletedAt: expect.any(Date),
        },
      });
    });

    it('should return 404 when property not found', async () => {
      mockPrisma.property.findFirst.mockResolvedValue(null);

      const result = await PropertyService.deleteProperty('non-existent', 'org-1');

      expect(result.success).toBe(false);
      expect(result.statusCode).toBe(404);
      expect(result.message).toBe('Property not found');
    });
  });

  describe('getProperties', () => {
    it('should successfully get properties with pagination', async () => {
      const mockProperties = [
        { id: 'property-1', title: 'Property 1' },
        { id: 'property-2', title: 'Property 2' },
      ];

      mockPrisma.property.findMany.mockResolvedValue(mockProperties as any);
      mockPrisma.property.count.mockResolvedValue(2);

      const result = await PropertyService.getProperties({
        organizationId: 'org-1',
        page: 1,
        limit: 20,
      });

      expect(result.success).toBe(true);
      expect(result.data?.properties).toEqual(mockProperties);
      expect(result.data?.pagination).toMatchObject({
        page: 1,
        limit: 20,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it('should handle filtering parameters', async () => {
      mockPrisma.property.findMany.mockResolvedValue([]);
      mockPrisma.property.count.mockResolvedValue(0);

      await PropertyService.getProperties({
        organizationId: 'org-1',
        city: 'Test City',
        propertyType: PropertyType.CONDO,
        minPrice: 100000,
        maxPrice: 500000,
        minBedrooms: 2,
        maxBedrooms: 4,
      });

      expect(mockPrisma.property.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          organizationId: 'org-1',
          isDeleted: false,
          city: { contains: 'Test City', mode: 'insensitive' },
          propertyType: PropertyType.CONDO,
          bedrooms: { gte: 2, lte: 4 },
          listPrice: { gte: 100000, lte: 500000 },
        }),
        skip: 0,
        take: 20,
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object),
      });
    });
  });
});
