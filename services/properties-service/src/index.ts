import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  clientsLimiter,
  createServiceLogger,
} from '@crm/shared';

import propertyRoutes from './routes/properties';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const PORT = process.env.PROPERTIES_SERVICE_PORT || 3003;
const logger = createServiceLogger('properties-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(clientsLimiter);

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    service: 'properties-service',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/v1/properties', propertyRoutes);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, () => {
    logger.info(`Properties service running on port ${PORT}`);
  });
}

export default app;
