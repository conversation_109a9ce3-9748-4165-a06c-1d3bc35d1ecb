import { Request, Response } from 'express';
import { prismaMock } from '../mocks/prisma';
import { AuthenticatedRequest } from '@crm/shared';
import { mockUser, mockTokens } from '../mocks/testData';

// Mock the auth service
jest.mock('../../services/authService');

// Mock ResponseHelper and other dependencies
jest.mock('@crm/shared', () => ({
  ...jest.requireActual('@crm/shared'),
  ResponseHelper: {
    success: jest.fn(),
    error: jest.fn(),
    unauthorized: jest.fn(),
    badRequest: jest.fn(),
    notFound: jest.fn(),
    internalServerError: jest.fn(),
  },
  JWTHelper: {
    verifyAccessToken: jest.fn(),
  },
  redisClient: {
    set: jest.fn(),
  },
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

// Import after mocking
import * as authController from '../../controllers/authController';
import * as authService from '../../services/authService';
import { ResponseHelper, JWTHelper, redisClient } from '@crm/shared';

const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockResponseHelper = ResponseHelper as jest.Mocked<typeof ResponseHelper>;
const mockJWTHelper = JWTHelper as jest.Mocked<typeof JWTHelper>;
const mockRedisClient = redisClient as jest.Mocked<typeof redisClient>;

describe('AuthController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockAuthenticatedRequest: Partial<AuthenticatedRequest>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      body: {},
      params: {},
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockAuthenticatedRequest = {
      ...mockRequest,
      user: mockUser,
    };
  });

  describe('login', () => {
    it('should successfully login user', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockLoginResult = {
        success: true,
        data: {
          user: mockUser,
          accessToken: mockTokens.accessToken,
          refreshToken: mockTokens.refreshToken,
        },
      };

      mockAuthService.loginUser.mockResolvedValue(mockLoginResult);

      // Act
      await authController.login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockAuthService.loginUser).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        mockLoginResult.data,
        'Login successful'
      );
    });

    it('should handle login failure', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const mockLoginResult = {
        success: false,
        message: 'Invalid email or password',
        statusCode: 401,
      };

      mockAuthService.loginUser.mockResolvedValue(mockLoginResult);

      // Act
      await authController.login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockResponseHelper.unauthorized).toHaveBeenCalledWith(
        mockResponse,
        'Invalid email or password'
      );
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123',
      };

      mockAuthService.loginUser.mockRejectedValue(new Error('Database error'));

      // Act
      await authController.login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockResponseHelper.internalServerError).toHaveBeenCalledWith(
        mockResponse,
        'Login failed'
      );
    });
  });

  describe('register', () => {
    it('should successfully register user', async () => {
      // Arrange
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        organizationId: 'org-123',
        role: 'AGENT',
      };

      mockRequest.body = registerData;

      const mockRegisterResult = {
        success: true,
        data: { ...mockUser, email: registerData.email },
      };

      mockAuthService.registerUser.mockResolvedValue(mockRegisterResult);

      // Act
      await authController.register(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockAuthService.registerUser).toHaveBeenCalledWith(registerData);
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        mockRegisterResult.data,
        'User registered successfully',
        201
      );
    });

    it('should handle registration failure', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Existing',
        lastName: 'User',
        organizationId: 'org-123',
      };

      const mockRegisterResult = {
        success: false,
        message: 'User with this email already exists in this organization',
        statusCode: 409,
      };

      mockAuthService.registerUser.mockResolvedValue(mockRegisterResult);

      // Act
      await authController.register(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockResponseHelper.badRequest).toHaveBeenCalledWith(
        mockResponse,
        'User with this email already exists in this organization'
      );
    });
  });

  describe('getCurrentUser', () => {
    it('should successfully get current user', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);

      // Act
      await authController.getCurrentUser(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          organizationId: true,
          createdAt: true,
          updatedAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              isActive: true,
            },
          },
        },
      });
      expect(mockResponseHelper.success).toHaveBeenCalledWith(mockResponse, mockUser);
    });

    it('should handle user not found', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act
      await authController.getCurrentUser(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.notFound).toHaveBeenCalledWith(mockResponse, 'User not found');
    });
  });

  describe('refreshToken', () => {
    it('should successfully refresh token', async () => {
      // Arrange
      mockRequest.body = {
        refreshToken: 'valid.refresh.token',
      };

      const mockRefreshResult = {
        success: true,
        data: mockTokens,
      };

      mockAuthService.refreshUserToken.mockResolvedValue(mockRefreshResult);

      // Act
      await authController.refreshToken(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockAuthService.refreshUserToken).toHaveBeenCalledWith('valid.refresh.token');
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        mockTokens,
        'Token refreshed successfully'
      );
    });

    it('should handle invalid refresh token', async () => {
      // Arrange
      mockRequest.body = {
        refreshToken: 'invalid.refresh.token',
      };

      const mockRefreshResult = {
        success: false,
        message: 'Invalid refresh token',
        statusCode: 401,
      };

      mockAuthService.refreshUserToken.mockResolvedValue(mockRefreshResult);

      // Act
      await authController.refreshToken(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockResponseHelper.unauthorized).toHaveBeenCalledWith(
        mockResponse,
        'Invalid refresh token'
      );
    });
  });

  describe('logout', () => {
    it('should successfully logout user', async () => {
      // Arrange
      const mockToken = 'mock.jwt.token';
      const mockDecoded = { exp: Math.floor(Date.now() / 1000) + 3600 }; // 1 hour from now

      mockAuthenticatedRequest.headers = {
        authorization: `Bearer ${mockToken}`,
      };

      mockJWTHelper.verifyAccessToken.mockReturnValue(mockDecoded as any);
      mockRedisClient.set.mockResolvedValue('OK' as any);

      // Act
      await authController.logout(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockJWTHelper.verifyAccessToken).toHaveBeenCalledWith(mockToken);
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        `blacklist:${mockToken}`,
        'true',
        expect.any(Number)
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        null,
        'Logout successful'
      );
    });

    it('should successfully logout user without token', async () => {
      // Arrange
      mockAuthenticatedRequest.headers = {};

      // Act
      await authController.logout(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        null,
        'Logout successful'
      );
    });
  });

  describe('changePassword', () => {
    it('should successfully change password', async () => {
      // Arrange
      mockAuthenticatedRequest.body = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123',
      };

      const mockChangePasswordResult = {
        success: true,
        data: null,
      };

      mockAuthService.changeUserPassword.mockResolvedValue(mockChangePasswordResult);

      // Act
      await authController.changePassword(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockAuthService.changeUserPassword).toHaveBeenCalledWith(
        mockUser.id,
        'oldpassword',
        'newpassword123'
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        null,
        'Password changed successfully'
      );
    });

    it('should handle password change failure', async () => {
      // Arrange
      mockAuthenticatedRequest.body = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
      };

      const mockChangePasswordResult = {
        success: false,
        message: 'Current password is incorrect',
        statusCode: 400,
      };

      mockAuthService.changeUserPassword.mockResolvedValue(mockChangePasswordResult);

      // Act
      await authController.changePassword(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.badRequest).toHaveBeenCalledWith(
        mockResponse,
        'Current password is incorrect'
      );
    });
  });
});
