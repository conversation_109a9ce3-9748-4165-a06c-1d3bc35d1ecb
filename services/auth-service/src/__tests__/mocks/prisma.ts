import { mockDeep, mockReset, DeepMockProxy } from 'jest-mock-extended';
import { PrismaClient } from '@crm/database';

// Create a deep mock of PrismaClient
export const prismaMock = mockDeep<PrismaClient>();

// Mock the prisma import
jest.mock('@crm/database', () => ({
  prisma: prismaMock,
  UserRole: {
    SUPER_ADMIN: 'SUPER_ADMIN',
    ADMIN: 'ADMIN',
    MANAGER: 'MANAGER',
    AGENT: 'AGENT',
    VIEWER: 'VIEWER',
  },
}));

// Reset mocks before each test
beforeEach(() => {
  mockReset(prismaMock);
});

export type MockPrisma = DeepMockProxy<PrismaClient>;
