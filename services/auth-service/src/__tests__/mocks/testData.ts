// Test data for auth service tests
export const mockOrganization = {
  id: 'org-123',
  name: 'Test Organization',
  slug: 'test-org',
  domain: 'test.com',
  logo: null,
  address: '123 Test St',
  phone: '******-0123',
  email: '<EMAIL>',
  website: 'https://test.com',
  timezone: 'UTC',
  currency: 'USD',
  subscriptionPlan: 'STARTER',
  subscriptionStatus: 'ACTIVE',
  trialEndsAt: null,
  subscriptionEndsAt: null,
  maxUsers: 5,
  maxProperties: 100,
  settings: null,
  isActive: true,
  isDeleted: false,
  deletedAt: null,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  password: '$2b$10$hashedpassword',
  firstName: 'Test',
  lastName: 'User',
  avatar: null,
  phone: '******-0123',
  title: 'Agent',
  organizationId: 'org-123',
  role: 'AGENT',
  timezone: 'UTC',
  language: 'en',
  preferences: null,
  isActive: true,
  isVerified: true,
  lastLoginAt: null,
  isDeleted: false,
  deletedAt: null,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
  organization: {
    id: 'org-123',
    name: 'Test Organization',
    slug: 'test-org',
    isActive: true,
  },
};

export const mockSuperAdmin = {
  ...mockUser,
  id: 'super-admin-123',
  email: '<EMAIL>',
  role: 'SUPER_ADMIN',
  firstName: 'Super',
  lastName: 'Admin',
};

export const mockAdmin = {
  ...mockUser,
  id: 'admin-123',
  email: '<EMAIL>',
  role: 'ADMIN',
  firstName: 'Admin',
  lastName: 'User',
};

export const mockInactiveUser = {
  ...mockUser,
  id: 'inactive-user-123',
  email: '<EMAIL>',
  isActive: false,
};

export const mockInactiveOrganization = {
  ...mockOrganization,
  id: 'inactive-org-123',
  name: 'Inactive Organization',
  slug: 'inactive-org',
  isActive: false,
};

export const mockUserWithInactiveOrg = {
  ...mockUser,
  id: 'user-inactive-org-123',
  email: '<EMAIL>',
  organizationId: 'inactive-org-123',
  organization: {
    id: 'inactive-org-123',
    name: 'Inactive Organization',
    slug: 'inactive-org',
    isActive: false,
  },
};

export const mockJWTPayload = {
  userId: 'user-123',
  email: '<EMAIL>',
  role: 'AGENT',
  organizationId: 'org-123',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 900, // 15 minutes
};

export const mockTokens = {
  accessToken: 'mock.access.token',
  refreshToken: 'mock.refresh.token',
};

export const mockRegisterRequest = {
  email: '<EMAIL>',
  password: 'password123',
  firstName: 'New',
  lastName: 'User',
  organizationId: 'org-123',
  role: 'AGENT' as const,
};
