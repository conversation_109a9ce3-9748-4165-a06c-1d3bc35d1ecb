import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  clientsLimiter,
  createServiceLogger,
} from '@crm/shared';

import clientRoutes from './routes/clients';



// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const PORT = process.env.CLIENTS_SERVICE_PORT || 3002;
const logger = createServiceLogger('clients-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting for clients endpoints
app.use(clientsLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'clients-service',
    version: '1.0.0',
  });
});

// Routes
app.use('/', clientRoutes);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🔐 Clients Service running on port ${PORT}`);
});

export default app;
