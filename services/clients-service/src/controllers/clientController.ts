import { Response } from 'express';
import { ClientService } from '../services/clientService';
import { ResponseHelper, createServiceLogger, AuthenticatedRequest } from '@crm/shared';
import { CreateClientInput } from '../types/client';
import { prisma } from '@crm/database';

const logger = createServiceLogger('clients-controller');

export const createClient = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    // Get user's organization ID from database
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const input: CreateClientInput = {
      ...req.body,
      organizationId: user.organizationId,
      ownerId: req.user.id
    };

    const result = await ClientService.createClient(input);

    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to create client' });
      return;
    }

    logger.info(`Client created by ${req.user.id} in organization ${user.organizationId}`);
    ResponseHelper.success(res, result.data, 'Client created successfully', 201);
  } catch (error) {
    logger.error('Error creating client:', error);
    ResponseHelper.error(res, 'Failed to create client', 500);
  }
};

export const getAllClients = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const { page = 1, limit = 20, status, type, source, search } = req.query;
    const filter: any = {
      organizationId: user.organizationId,
      isDeleted: false
    };

    if (status) filter.status = status;
    if (type) filter.type = type;
    if (source) filter.source = source;
    if (search) {
      filter.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    const result = await ClientService.getClients({
      organizationId: user.organizationId,
      page: Number(page),
      limit: Number(limit),
      status: status as string,
      clientType: type as string,
      source: source as string,
      search: search as string
    });

    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to fetch clients' });
      return;
    }

    ResponseHelper.success(res, result.data);
  } catch (error) {
    logger.error('Error fetching clients:', error);
    ResponseHelper.error(res, 'Failed to fetch clients', 500);
  }
};

export const getClientById = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    // Fetch client by id only
    const client = await prisma.client.findUnique({ where: { id: req.params.id } });
    if (!client || client.isDeleted) {
      res.status(404).json({ success: false, message: 'Client not found' });
      return;
    }
    if (client.organizationId !== user.organizationId) {
      res.status(403).json({ success: false, message: 'Access denied to this client' });
      return;
    }

    const result = await ClientService.getClientById(req.params.id, user.organizationId);
    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to fetch client' });
      return;
    }
    ResponseHelper.success(res, result.data);
  } catch (error) {
    logger.error('Error fetching client:', error);
    ResponseHelper.error(res, 'Failed to fetch client', 500);
  }
};

export const updateClient = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    // Fetch client by id only
    const client = await prisma.client.findUnique({ where: { id: req.params.id } });
    if (!client || client.isDeleted) {
      res.status(404).json({ success: false, message: 'Client not found' });
      return;
    }
    if (client.organizationId !== user.organizationId) {
      res.status(403).json({ success: false, message: 'Access denied to this client' });
      return;
    }

    const result = await ClientService.updateClient(
      req.params.id,
      user.organizationId,
      req.body
    );
    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to update client' });
      return;
    }
    logger.info(`Client ${req.params.id} updated by ${req.user.id}`);
    ResponseHelper.success(res, result.data, 'Client updated successfully');
  } catch (error) {
    logger.error('Error updating client:', error);
    ResponseHelper.error(res, 'Failed to update client', 500);
  }
};

export const deleteClient = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    // Fetch client by id only
    const client = await prisma.client.findUnique({ where: { id: req.params.id } });
    if (!client || client.isDeleted) {
      res.status(404).json({ success: false, message: 'Client not found' });
      return;
    }
    if (client.organizationId !== user.organizationId) {
      res.status(403).json({ success: false, message: 'Access denied to this client' });
      return;
    }

    const result = await ClientService.deleteClient(req.params.id, user.organizationId);
    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to delete client' });
      return;
    }
    logger.info(`Client ${req.params.id} deleted by ${req.user.id}`);
    ResponseHelper.success(res, null, 'Client deleted successfully');
  } catch (error) {
    logger.error('Error deleting client:', error);
    ResponseHelper.error(res, 'Failed to delete client', 500);
  }
};

export const searchClients = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const { query, page = 1, limit = 20, status, type } = req.query;

    if (!query) {
      ResponseHelper.badRequest(res, 'Search query is required');
      return;
    }

    const result = await ClientService.searchClients({
      query: query as string,
      organizationId: user.organizationId,
      page: Number(page),
      limit: Number(limit),
      status: status as string,
      clientType: type as string
    });

    if (!result.success) {
      res.status(result.statusCode || 500).json({ success: false, message: result.message || 'Failed to search clients' });
      return;
    }

    ResponseHelper.success(res, result.data);
  } catch (error) {
    logger.error('Error searching clients:', error);
    ResponseHelper.error(res, 'Failed to search clients', 500);
  }
};

