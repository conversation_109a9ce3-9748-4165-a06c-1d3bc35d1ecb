import request from 'supertest';
import app from '../../index';
import { testDb, setupTestData, cleanupTestData } from './setup';
import { JWTHelper } from '@crm/shared';

describe('Clients Integration Tests', () => {
  let testData: any;
  let agentToken: string;
  let anotherAgentToken: string;

  beforeAll(async () => {
    testData = await setupTestData();

    // Generate tokens for different users
    agentToken = JWTHelper.generateTokenPair({
      userId: testData.agent.id,
      email: testData.agent.email,
      role: testData.agent.role,
      organizationId: testData.agent.organizationId,
    }).accessToken;

    anotherAgentToken = JWTHelper.generateTokenPair({
      userId: testData.anotherAgent.id,
      email: testData.anotherAgent.email,
      role: testData.anotherAgent.role,
      organizationId: testData.anotherAgent.organizationId,
    }).accessToken;
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('GET /v1/clients', () => {
    it('should successfully get clients for organization', async () => {
      const response = await request(app)
        .get('/v1/clients')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.clients).toHaveLength(2);
      expect(response.body.data.clients).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            firstName: 'Active',
            lastName: 'Client',
            email: '<EMAIL>',
            status: 'ACTIVE',
          }),
          expect.objectContaining({
            firstName: 'Inactive',
            lastName: 'Client',
            email: '<EMAIL>',
            status: 'INACTIVE',
          })
        ])
      );
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/v1/clients')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should return different clients for different organization', async () => {
      const response = await request(app)
        .get('/v1/clients')
        .set('Authorization', `Bearer ${anotherAgentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.clients).toHaveLength(1);
      expect(response.body.data.clients[0]).toMatchObject({
        firstName: 'Another',
        lastName: 'Client',
        email: '<EMAIL>',
        status: 'ACTIVE',
      });
    });
  });

  describe('GET /v1/clients/:id', () => {
    it('should successfully get client by ID', async () => {
      const response = await request(app)
        .get(`/v1/clients/${testData.activeClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testData.activeClient.id,
        firstName: 'Active',
        lastName: 'Client',
        email: '<EMAIL>',
        phone: '******-0001',
        status: 'ACTIVE',
      });
    });

    it('should fail when accessing different organization client', async () => {
      const response = await request(app)
        .get(`/v1/clients/${testData.anotherOrgClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this client');
    });

    it('should fail with nonexistent client ID', async () => {
      const response = await request(app)
        .get('/v1/clients/nonexistent-client-id')
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Client not found');
    });
  });

  describe('POST /v1/clients', () => {
    it('should successfully create client', async () => {
      const newClientData = {
        firstName: 'New',
        lastName: 'Client',
        email: '<EMAIL>',
        phone: '******-0004',
        status: 'ACTIVE',
      };

      const response = await request(app)
        .post('/v1/clients')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(newClientData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        firstName: 'New',
        lastName: 'Client',
        email: '<EMAIL>',
        phone: '******-0004',
        status: 'ACTIVE',
      });

      // Verify client was created in database
      const createdClient = await testDb.client.findUnique({
        where: { id: response.body.data.id },
      });
      expect(createdClient).toBeTruthy();
    });

    it('should fail to create client with duplicate email', async () => {
      const duplicateClientData = {
        firstName: 'Duplicate',
        lastName: 'Client',
        email: '<EMAIL>', // This email already exists
        phone: '******-0005',
        status: 'ACTIVE',
      };

      const response = await request(app)
        .post('/v1/clients')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(duplicateClientData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Client with this email already exists');
    });

    it('should fail with invalid data', async () => {
      const invalidClientData = {
        firstName: 'A', // Too short
        lastName: 'B', // Too short
        email: 'invalid-email',
        phone: 'invalid-phone',
      };

      const response = await request(app)
        .post('/v1/clients')
        .set('Authorization', `Bearer ${agentToken}`)
        .send(invalidClientData)
        .expect(422);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /v1/clients/:id', () => {
    it('should successfully update client', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Client',
        phone: '******-0006',
      };

      const response = await request(app)
        .put(`/v1/clients/${testData.activeClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        firstName: 'Updated',
        lastName: 'Client',
        phone: '******-0006',
      });

      // Verify client was updated in database
      const updatedClient = await testDb.client.findUnique({
        where: { id: testData.activeClient.id },
      });
      expect(updatedClient?.firstName).toBe('Updated');
    });

    it('should fail to update different organization client', async () => {
      const updateData = {
        firstName: 'Hacked',
        lastName: 'Client',
      };

      const response = await request(app)
        .put(`/v1/clients/${testData.anotherOrgClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this client');
    });
  });

  describe('DELETE /v1/clients/:id', () => {
    it('should successfully soft delete client', async () => {
      const response = await request(app)
        .delete(`/v1/clients/${testData.activeClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Client deleted successfully');

      // Verify client was soft deleted
      const deletedClient = await testDb.client.findUnique({
        where: { id: testData.activeClient.id },
      });
      expect(deletedClient?.isDeleted).toBe(true);
      expect(deletedClient?.isActive).toBe(false);
      expect(deletedClient?.deletedAt).toBeTruthy();
    });

    it('should fail to delete different organization client', async () => {
      const response = await request(app)
        .delete(`/v1/clients/${testData.anotherOrgClient.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this client');
    });
  });
});
